2025-07-09 14:47:24.238  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8242b1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:24.803  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-09 14:47:26.917  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-09 14:47:26.918  WARN [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-09 14:47:26.921  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.standalone.StandaloneApplication     : The following profiles are active: alone
2025-07-09 14:47:33.312  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-09 14:47:33.314  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-09 14:47:33.600  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 272ms. Found 0 Redis repository interfaces.
2025-07-09 14:47:33.731  WARN [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-09 14:47:34.005  WARN [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-09 14:47:34.425  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=978eec78-43f9-31a3-8817-6a8ef2ebb342
2025-07-09 14:47:34.446  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-09 14:47:34.452  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-09 14:47:34.465  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-09 14:47:34.552  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$90207b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:34.555  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$c2344ce8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:34.652  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$a1cdc047] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:34.980  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ac0a2822] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.028  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$87ad059f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.079  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$c3ce3745] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.134  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.189  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.202  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$16fbe351] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:35.243  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8242b1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-09 14:47:36.188  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8888 (http)
2025-07-09 14:47:36.337  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 9393 ms
2025-07-09 14:47:39.324  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.b.a.e.web.ServletEndpointRegistrar   : Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
2025-07-09 14:47:39.413  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-09 14:47:39.481  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-09 14:47:39.564  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-09 14:47:39.569  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-09 14:47:39.569  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-09 14:47:39.569  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-09 14:47:39.569  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-09 14:47:40.375  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-09 14:47:43.090 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-09 14:47:43.091  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-09 14:47:44.475  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-09 14:47:44.489  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-09 14:47:45.017  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-09 14:47:45.104 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5e3569e8],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@7925da37],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@7e051d8e],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@7624028],]
2025-07-09 14:47:45.105 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-09 14:47:54.845  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-09 14:47:54.854  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-09 14:47:56.008  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-09 14:47:57.681  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-09 14:47:57.936 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-09 14:47:57.952 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-09 14:47:57.972 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 9
2025-07-09 14:47:57.994  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-09 14:47:57.994  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-09 14:47:57.995  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-09 14:47:57.995  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-09 14:47:57.996  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-09 14:47:57.997  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-09 14:47:57.997  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-09 14:47:57.997  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-09 14:47:57.997  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-09 14:47:58.000  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-09 14:48:02.101  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-09 14:48:02.101  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-09 14:48:02.101  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-09 14:48:02.153  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-09 14:48:03.581  WARN [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 14:48:05.184  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-09 14:48:06.495  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-09 14:48:06.496  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-09 14:48:06.520  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-09 14:48:09.386  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
2025-07-09 14:48:09.813  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-09 14:48:09.813  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-09 14:48:09.902  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8888 (http) with context path ''
2025-07-09 14:48:09.902  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8888
2025-07-09 14:48:10.325  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.standalone.StandaloneApplication     : Started StandaloneApplication in 47.97 seconds (JVM running for 48.497)
2025-07-09 14:48:10.354  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-09 14:48:10.354  WARN [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
2025-07-09 14:48:10.359  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-09 14:48:10.407 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-09 14:48:10.440 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-09 14:48:10.441 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-09 14:48:10.454 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-09 14:48:10.455 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-09 14:48:10.455 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-09 14:48:10.469 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 294
2025-07-09 14:48:10.473  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-09 14:48:10.473  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-09 14:48:10.474  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
2025-07-09 14:48:10.489 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-09 14:48:10.772  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-09 14:48:10.776  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-09 14:48:10.777  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-09 14:48:10.806  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-09 14:48:12.097  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-09 14:48:12.097  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-09 14:48:12.102 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-09 14:48:12.102 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-09 14:48:12.109 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-09 14:48:12.112 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-09 14:48:12.112 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:12.122 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-09 14:48:12.772 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-09 14:48:12.772 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:12.778 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-09 14:48:12.928 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-09 14:48:12.928 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:12.939 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-09 14:48:14.110 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-09 14:48:14.110 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.116 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-09 14:48:14.159 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-09 14:48:14.160 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.166 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-09 14:48:14.228 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-09 14:48:14.228 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.237 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-09 14:48:14.284 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-09 14:48:14.284 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.293 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-09 14:48:14.525 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-09 14:48:14.526 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.533 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-09 14:48:14.586 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-09 14:48:14.586 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.594 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-09 14:48:14.691 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-09 14:48:14.692 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-09 14:48:14.699 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-09 14:48:14.728  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-09 14:48:14.728  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-09 14:48:14.728  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-09 14:48:14.729  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-09 14:48:14.729  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-09 14:48:14.735 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-09 14:48:14.735 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-09 14:48:14.731(Timestamp), 1(Integer)
2025-07-09 14:48:14.749 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-09 14:48:14.750  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-09 14:48:14.750  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-09 14:48:14.750  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-09 14:48:14.832 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.834 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-09 14:48:14.841 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.842 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.842 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-09 14:48:14.849 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.849 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.850 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-09 14:48:14.856 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.857 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.857 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-09 14:48:14.864 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.865 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.865 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-09 14:48:14.872 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.872 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.872 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-09 14:48:14.878 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.879 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.879 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-09 14:48:14.885 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.885 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.885 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-09 14:48:14.891 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.892 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:14.892 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-09 14:48:14.898 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:14.898 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-09 14:48:14.899 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-09 14:48:14.911 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-09 14:48:14.912 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-09 14:48:14.912 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-09 14:48:14.923 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-09 14:48:14.923 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-09 14:48:14.923 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-09 14:48:14.936 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-09 14:48:14.937 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-09 14:48:14.937 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-09 14:48:14.948 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-09 14:48:14.949 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-09 14:48:14.949 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-09 14:48:14.961 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-09 14:48:14.962 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-09 14:48:14.962 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-09 14:48:14.973 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-09 14:48:14.973 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-09 14:48:14.973 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-09 14:48:14.985 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-09 14:48:14.985 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-09 14:48:14.986 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-09 14:48:14.998 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-09 14:48:15.000 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-09 14:48:15.000 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-09 14:48:15.007 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-09 14:48:15.008 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.008 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-09 14:48:15.014 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.014 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.015 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-09 14:48:15.021 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.022 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.022 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-09 14:48:15.027 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.027 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.028 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-09 14:48:15.034 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.034 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-09 14:48:15.034 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-09 14:48:15.046 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-09 14:48:15.047 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.047 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-09 14:48:15.053 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.054 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-09 14:48:15.055 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-09 14:48:15.070 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-09 14:48:15.070  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-09 14:48:15.071 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-09 14:48:15.071 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-09 14:48:15.078 DEBUG [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-09 14:48:15.078  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-09 14:48:15.078  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-09 14:48:15.078  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-09 14:48:15.964  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [      Thread-12] c.xcwlkj.msgque.que.XcRocektMqConsumer   : 监听：JKYT_RSTJ_XXTS,启动成功！
2025-07-09 14:48:16.576  INFO [,,,] [eeip-standalone-service,,,,] 52104 --- [      Thread-24] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
