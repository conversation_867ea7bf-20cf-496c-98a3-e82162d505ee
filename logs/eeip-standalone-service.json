{"@timestamp":"2025-07-09T06:47:24.238Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8242b1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:24.803Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-09T06:47:26.917Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-09T06:47:26.918Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-09T06:47:26.921Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-09T06:47:33.312Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-09T06:47:33.314Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-09T06:47:33.600Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 272ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-09T06:47:33.731Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-09T06:47:34.005Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-09T06:47:34.425Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=978eec78-43f9-31a3-8817-6a8ef2ebb342"}
{"@timestamp":"2025-07-09T06:47:34.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-09T06:47:34.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-09T06:47:34.465Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-09T06:47:34.552Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$90207b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:34.555Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$c2344ce8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:34.652Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$a1cdc047] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:34.980Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ac0a2822] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.028Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$87ad059f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.079Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$c3ce3745] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.134Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.189Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.202Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$16fbe351] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:35.243Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8242b1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-09T06:47:36.188Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-09T06:47:36.337Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 9393 ms"}
{"@timestamp":"2025-07-09T06:47:39.324Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-09T06:47:39.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-09T06:47:39.481Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-09T06:47:39.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-09T06:47:39.569Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-09T06:47:39.569Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-09T06:47:39.569Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-09T06:47:39.569Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-09T06:47:40.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-09T06:47:43.090Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-09T06:47:43.091Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-09T06:47:44.475Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-09T06:47:44.489Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-09T06:47:45.017Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-09T06:47:45.104Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5e3569e8],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@7925da37],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@7e051d8e],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@7624028],]"}
{"@timestamp":"2025-07-09T06:47:45.105Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-09T06:47:54.845Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-09T06:47:54.854Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-09T06:47:56.008Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-09T06:47:57.681Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-09T06:47:57.936Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-09T06:47:57.952Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:47:57.972Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 9"}
{"@timestamp":"2025-07-09T06:47:57.994Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-09T06:47:57.994Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-09T06:47:57.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-09T06:47:57.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-09T06:47:57.996Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-09T06:47:57.997Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-09T06:47:57.997Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-09T06:47:57.997Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-09T06:47:57.997Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-09T06:47:58.000Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-09T06:48:02.101Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-09T06:48:02.101Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-09T06:48:02.101Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-09T06:48:02.153Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-09T06:48:03.581Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-09T06:48:05.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-09T06:48:06.495Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-09T06:48:06.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-09T06:48:06.520Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-09T06:48:09.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-09T06:48:09.813Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-09T06:48:09.813Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-09T06:48:09.902Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-09T06:48:09.902Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-09T06:48:10.325Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 47.97 seconds (JVM running for 48.497)"}
{"@timestamp":"2025-07-09T06:48:10.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-09T06:48:10.354Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-09T06:48:10.359Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-09T06:48:10.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-09T06:48:10.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-09T06:48:10.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-09T06:48:10.454Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:10.455Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-09T06:48:10.455Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:10.469Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 294"}
{"@timestamp":"2025-07-09T06:48:10.473Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-09T06:48:10.473Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-09T06:48:10.474Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-09T06:48:10.489Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-09T06:48:10.772Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-09T06:48:10.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-09T06:48:10.777Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-09T06:48:10.806Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-09T06:48:12.097Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-09T06:48:12.097Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-09T06:48:12.102Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-09T06:48:12.102Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-09T06:48:12.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-09T06:48:12.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:12.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:12.122Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-09T06:48:12.772Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:12.772Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:12.778Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-09T06:48:12.928Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-09T06:48:12.928Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:12.939Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-09T06:48:14.110Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:14.110Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.116Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-09T06:48:14.159Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-09T06:48:14.160Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.166Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-09T06:48:14.228Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:14.228Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.237Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-09T06:48:14.284Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-09T06:48:14.284Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.293Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-09T06:48:14.525Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:14.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.533Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-09T06:48:14.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:14.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.594Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-09T06:48:14.691Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-09T06:48:14.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.699Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-09T06:48:14.728Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-09T06:48:14.728Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-09T06:48:14.728Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-09T06:48:14.729Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-09T06:48:14.729Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-09T06:48:14.735Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-09T06:48:14.735Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-09 14:48:14.731(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-09T06:48:14.749Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.750Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-09T06:48:14.750Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-09T06:48:14.750Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-09T06:48:14.832Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.834Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-09T06:48:14.841Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.842Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.842Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-09T06:48:14.849Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.849Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.850Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-09T06:48:14.856Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.857Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.857Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-09T06:48:14.864Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.865Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.865Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-09T06:48:14.872Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.872Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.872Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-09T06:48:14.878Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-09T06:48:14.885Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.885Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.885Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-09T06:48:14.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.892Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:14.892Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-09T06:48:14.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:14.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-09T06:48:14.899Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.911Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.912Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-09T06:48:14.912Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.923Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.923Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-09T06:48:14.923Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.936Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.937Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-09T06:48:14.937Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.948Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.949Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-09T06:48:14.949Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.961Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-09T06:48:14.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.973Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.973Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-09T06:48:14.973Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.985Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:14.985Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-09T06:48:14.986Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:14.998Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:15.000Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-09T06:48:15.000Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-09T06:48:15.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-09T06:48:15.008Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.008Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-09T06:48:15.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-09T06:48:15.021Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-09T06:48:15.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.028Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-09T06:48:15.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-09T06:48:15.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-09T06:48:15.046Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:15.047Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.047Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-09T06:48:15.053Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.054Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-09T06:48:15.055Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-09T06:48:15.070Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-09T06:48:15.070Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-09T06:48:15.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-09T06:48:15.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-09T06:48:15.078Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-09T06:48:15.078Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-09T06:48:15.078Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-09T06:48:15.078Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-09T06:48:15.964Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"Thread-12","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-09T06:48:16.576Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"52104","thread":"Thread-24","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
