/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.resp.user;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 查询用户详情响应
 * <AUTHOR>
 * @version $Id: UserDetailRespModel.java, v 0.1 2024年04月11日 15时13分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDetailRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 用户id号 */
    private String id;
    /** 用户名 */
    private String userName;
    /** 用户姓名 */
    private String name;
    /** 部门名称 */
    private String departmentName;
    /** 学校名称 */
    private String schoolName;
    /** 学工号 */
    private String xgh;
    /** 性别码 */
    private String xbm;
    /** 二级学科码 */
    private String ejxkm;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}