/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.vo.attendancerule;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;


/**
 * 考勤规则列表vo
 * <AUTHOR>
 * @version $Id: RuleListVO.java, v 0.1 2024年04月16日 14时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class RemoteRuleListVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 规则列表 */
    private List<RemoteRuleItemVO> ruleList;
    /** 总条数 */
    private Integer totalRows;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
