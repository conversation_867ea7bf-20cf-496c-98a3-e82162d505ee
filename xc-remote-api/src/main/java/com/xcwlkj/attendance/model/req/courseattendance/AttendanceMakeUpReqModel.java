/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.req.courseattendance;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;


/**
 * 考勤补卡请求
 * <AUTHOR>
 * @version $Id: AttendanceMakeUpReqModel.java, v 0.1 2024年04月02日 09时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AttendanceMakeUpReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 学号 */
    @NotBlank(message = "学号不能为空")
    private String xh;
    /** 排课表编号 */
    @NotBlank(message = "排课表编号不能为空")
    private String pkbbh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}