/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.campus.model.vo.photo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;


/**
 * 校园相册列表vo
 * <AUTHOR>
 * @version $Id: SelectZhxyXcspListVO.java, v 0.1 2024年04月02日 16时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SelectZhxyXcspListVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 相册列表 */
    private List<RemoteXcItemVO> xcList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
