/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.resp.classevaluation;

import com.xcwlkj.campus.model.vo.classevaluation.ClassRankItemVO;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 班级评价-班级排名响应
 * <AUTHOR>
 * @version $Id: GetClassRankingRespModel.java, v 0.1 2025年05月21日 10时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetClassRankingRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /**  */
    private List<ClassRankItemVO> classRankList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}