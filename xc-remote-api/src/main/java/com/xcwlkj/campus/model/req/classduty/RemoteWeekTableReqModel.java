/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.classduty;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 班级值日列表请求
 * <AUTHOR>
 * @version $Id: WeekTableReqModel.java, v 0.1 2025年06月25日 13时54分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteWeekTableReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 开课学年度 */
    @NotBlank(message = "开课学年度不能为空")
    private String kkxnd;
    /** 开课学期码 */
    @NotBlank(message = "开课学期码不能为空")
    private String kkxqm;
    /** 查询周次 */
    @NotNull(message = "查询周次不能为空")
    private Integer cxzc;
    /** 教室号 */
    private String jsh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}