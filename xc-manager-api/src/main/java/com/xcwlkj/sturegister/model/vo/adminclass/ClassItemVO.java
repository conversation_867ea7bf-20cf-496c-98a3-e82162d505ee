/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.model.vo.adminclass;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 列表班级vo
 *
 * <AUTHOR>
 * @version $Id: ClassItemVO.java, v 0.1 2022年09月29日 13时23分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ClassItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 班级名称
     */
    private String bjmc;
    /**
     * 建班年月
     */
    private String jbny;
    /**
     * 所属年级
     */
    private String ssnj;
    /**
     * 是否订单班
     */
    private String sfddb;
    /**
     * 单位号
     */
    private String dwh;
    /**
     * 编号
     */
    private String bah;
    /**
     * 专业名称
     */
    private String zymc;
    /**
     * 单位名称
     */
    private String dwmc;
    /**
     * 班主任工号(逗号分隔)
     */
    private String bzrgh;
    /**
     * 班主任名称
     */
    private String bzrmc;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
