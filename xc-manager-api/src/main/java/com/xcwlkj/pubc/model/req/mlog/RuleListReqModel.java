/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.pubc.model.req.mlog;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 日志触发规则列表查询请求
 * <AUTHOR>
 * @version $Id: RuleListReqModel.java, v 0.1 2020年12月03日 17时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RuleListReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 每页条数 */
    @NotNull(message = "每页条数不能为空")
    private Integer pageSize;
    /** 页码 */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}