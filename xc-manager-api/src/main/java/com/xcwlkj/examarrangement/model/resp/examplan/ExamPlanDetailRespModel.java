/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.resp.examplan;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 考试计划详情响应
 *
 * <AUTHOR>
 * @version $Id: ExamPlanAddRespModel.java, v 0.1 2022年10月08日 09时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExamPlanDetailRespModel extends RemoteRespBaseModel {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * 编号
     */
    private String bh;
    /**
     * 考试名称
     */
    private String ksmc;
    /**
     * 考试说明
     */
    private String kssm;
    /**
     * 考试类型
     */
    private String kslx;
    /**
     * 学年
     */
    private String xn;
    /**
     * 学期
     */
    private String xq;
    /**
     * 开始时间
     */
    private String kskssj;
    /**
     * 结束时间
     */
    private String ksjssj;
    /**
     * 状态
     */
    private String zt;
    /**
     * 是否默认
     */
    private String sfmr;
    /**
     * 修改时间
     */
    private String xgsj;
    /**
     * 创建时间
     */
    private String cjsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}