/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.examarrangement.model.vo.examcourseplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 考试课程计划详情vo
 * <AUTHOR>
 * @version $Id: ExamCoursePlanDetailVO.java, v 0.1 2023年07月13日 16时45分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ExamCoursePlanDetailVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 编号 */
    private String bh;
    /** 考试计划编号 */
    private String ksjhid;
    /** 考试名称 */
    private String ksmc;
    /** 学年 */
    private String xn;
    /** 学期 */
    private String xq;
    /** 周次 */
    private String zc;
    /** 课程号 */
    private String kch;
    /** 课程名称 */
    private String kcmc;
    /** 考试批次号 */
    private String kspch;
    /** 考试日期 */
    private String ksrq;
    /** 考试开始时间 */
    private String kskssj;
    /** 考试结束时间 */
    private String ksjssj;
    /** 考试时长 */
    private String kssc;
    /** 考试方式类型码 */
    private String ksfslxm;
    /** 考试类型 */
    private String kslx;
    /** 修改时间 */
    private String xgsj;
    /** 创建时间 */
    private String cjsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
