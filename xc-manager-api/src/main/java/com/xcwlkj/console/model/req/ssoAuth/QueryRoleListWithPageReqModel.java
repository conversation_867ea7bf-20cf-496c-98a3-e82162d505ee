/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2015-2025 All Rights Reserved.
 */
package com.xcwlkj.console.model.req.ssoAuth;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * <AUTHOR>
 * @version $Id: QueryRoleListWithPageReqModel.java, v 0.1 2019年7月20日 下午4:54:00 White Wolf Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryRoleListWithPageReqModel extends RemoteReqBaseModel {

    /**  */
    private static final long serialVersionUID = 6111276235661246422L;

    private String            name;

    private String            status;

    private Integer pageNum;

    private Integer pageSize;

}
