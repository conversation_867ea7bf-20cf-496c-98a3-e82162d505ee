/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.console.model.req.department;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 部门新增请求
 * <AUTHOR>
 * @version $Id: AddDepartmentReqModel.java, v 0.1 2019年08月20日 15时31分 XcDev Exp $
 */
 
@Data
@EqualsAndHashCode(callSuper = false)
public class AddDepartmentReqModel extends RemoteReqBaseModel {

    private static final long serialVersionUID = -1L;
	
    /** 上级部门id */
    @NotBlank(message = "上级部门id不能为空")
    private String            parentId;
    /** 部门名称 */
    @NotBlank(message = "部门名称不能为空")
    private String            name;
    /** 负责人 */
    @NotBlank(message = "负责人不能为空")
    private String            leader;
    /** 联系电话 */
    @NotBlank(message = "联系电话不能为空")
    private String            phone;
    /** 部门简介 */
    private String            description;
    /** 部门状态:1正常,0停用 */
    @NotNull(message = "部门状态不能为空")
    private Integer            status;
    /** 部门服务器地址 */
    private String            sipUrl;
    /** 排序 */
    private Integer            orderNum;
    /** 类型：机构：mechanism  部门：department */
    @NotBlank(message = "部门类型不能为空")
    private String            type;

}