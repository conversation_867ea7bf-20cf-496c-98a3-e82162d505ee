/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.resp.courseattendance;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.attendance.model.vo.courseattendance.TeacherAttendanceItemVO;
import java.util.List;


/**
 * 教师考勤列表响应
 * <AUTHOR>
 * @version $Id: TeacherAttendanceListRespModel.java, v 0.1 2023年07月17日 09时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeacherAttendanceListRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 教师考勤列表 */
    private List<TeacherAttendanceItemVO> teacherAttendanceList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}