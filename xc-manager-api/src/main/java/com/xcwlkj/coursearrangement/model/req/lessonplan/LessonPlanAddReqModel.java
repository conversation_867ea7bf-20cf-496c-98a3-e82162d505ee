/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.model.req.lessonplan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;


/**
 * 学期开课计划添加请求
 *
 * <AUTHOR>
 * @version $Id: LessonPlanAddReqModel.java, v 0.1 2022年10月08日 09时34分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LessonPlanAddReqModel extends RemoteReqBaseModel {
    /**
     * 序列ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * 开课学年度
     */
    @NotBlank(message = "开课学年度不能为空")
    private String kkxnd;
    /**
     * 开课学期码
     */
    @NotBlank(message = "开课学期码不能为空")
    private String kkxqm;
    /**
     * 课程号
     */
    @NotBlank(message = "课程号不能为空")
    private String kch;
    /**
     * 课程名称
     */
    @NotBlank(message = "课程名称不能为空")
    private String kcmc;
    /**
     * 课程英文名称
     */
    private String kcywmc;
    /**
     * 课程属性码
     */
    private String kcsxm;
    /**
     * 授课方式码
     */
    private String skfsm;
    /**
     * 预修课程
     */
    private String yxkc;
    /**
     * 考试方式码
     */
    private String ksfsm;
    /**
     * 学分
     */
    private BigDecimal xf;
    /**
     * 周学时
     */
    private BigDecimal zhxs;
    /**
     * 总学时
     */
    private Integer zxs;
    /**
     * 理论学时
     */
    private Integer llxs;
    /**
     * 实验学时
     */
    private Integer syxs;
    /**
     * 课程简介
     */
    private String kcjj;
    /**
     * 课程负责人号
     */
    private String kcfzrh;
    /**
     * 课程开设单位号
     */
    @NotBlank(message = "课程开设单位号不能为空")
    private String kcksdwh;
    /**
     * 实践学时
     */
    private Integer sjxs;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}