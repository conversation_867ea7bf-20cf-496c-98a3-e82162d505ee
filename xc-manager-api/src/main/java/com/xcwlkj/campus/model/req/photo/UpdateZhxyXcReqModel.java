/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.req.photo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 修改校园相册请求
 * <AUTHOR>
 * @version $Id: UpdateZhxyXcReqModel.java, v 0.1 2023年07月20日 14时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateZhxyXcReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 编号 */
    @NotNull(message = "编号不能为空")
    private Integer bh;
    /** 名称 */
    @NotBlank(message = "名称不能为空")
    private String mc;
    /** 封面图 */
    private String fmt;
    /** 内容简介 */
    private String nrjj;
    /** 类型             图片image             视频video */
    @NotBlank(message = "类型             图片image             视频video不能为空")
    private String lx;
    /** 发布范围类型             按照教室 classroom             按照班级 class */
    @NotBlank(message = "发布范围类型             按照教室 classroom             按照班级 class不能为空")
    private String fbfwlx;
    /** 班级教室号 */
    private List<String> bjjshList;
    /** 班级/教室名称 */
    private List<String> bjjsmcList;
    /** 班级教室标记 */
    private List<String> bjjsbjList;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}