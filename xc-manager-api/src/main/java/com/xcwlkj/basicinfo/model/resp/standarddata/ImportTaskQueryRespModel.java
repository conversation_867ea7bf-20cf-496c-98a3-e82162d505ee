/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.basicinfo.model.resp.standarddata;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 查询导入结果响应
 * <AUTHOR>
 * @version $Id: ImportTaskQueryRespModel.java, v 0.1 2022年09月29日 15时42分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportTaskQueryRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 任务状态 */
    private String rwzt;
    /** 执行结果 */
    private String zxjg;
    /** 结果描述 */
    private String jgms;
    /** 执行完成时间 */
    private String zxwcsj;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}