/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.req.examroom;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 场地-考场-详情请求
 * <AUTHOR>
 * @version $Id: DetailsV1ReqModel.java, v 0.1 2022年10月09日 09时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DetailsV1ReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 教室号 */
    @NotBlank(message = "教室号不能为空")
    private String jsh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}