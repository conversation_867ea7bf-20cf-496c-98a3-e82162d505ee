{"@timestamp":"2025-07-08T07:06:20.836Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c554f9cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:21.622Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T07:06:23.750Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-08T07:06:23.752Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/identityverify-service/develop\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-08T07:06:23.762Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.i.service.impl.SjwjfwsccsServiceTest","rest":"The following profiles are active: develop"}
{"@timestamp":"2025-07-08T07:06:26.214Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T07:06:26.217Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T07:06:26.313Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 80ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T07:06:26.392Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T07:06:26.839Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=1ca7dee1-5cc3-38d7-88ef-1caf164a6747"}
{"@timestamp":"2025-07-08T07:06:27.079Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T07:06:27.085Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T07:06:27.096Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T07:06:27.191Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$632d664] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.192Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$bf651b94] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.286Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$9efe8ef3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.590Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a93af6ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.640Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.identityverify.config.RedisConfig$$EnhancerBySpringCGLIB$$8fa9d59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.701Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.identityverify.config.AppConfig$$EnhancerBySpringCGLIB$$3436ba7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.746Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$142cb1fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.787Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:27.865Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c554f9cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:28.453Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T07:06:30.828Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T07:06:30.919Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T07:06:31.705Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksBmxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsFaceFeatureUtils': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'HsFaceFeature.HsFaceFeatureCreatorPath' in value \"${HsFaceFeature.HsFaceFeatureCreatorPath}\""}
{"@timestamp":"2025-07-08T07:06:31.718Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.b.a.l.ConditionEvaluationReportLoggingListener","rest":"\r\n\r\nError starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled."}
{"@timestamp":"2025-07-08T07:06:31.725Z","severity":"ERROR","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-08T07:06:32.228Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c554f9cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:32.682Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T07:06:34.736Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-08T07:06:34.736Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/identityverify-service/develop\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-08T07:06:34.739Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.i.service.impl.SjwjfwsccsServiceTest","rest":"The following profiles are active: develop"}
{"@timestamp":"2025-07-08T07:06:35.955Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T07:06:35.956Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T07:06:36.045Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 89ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T07:06:36.185Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=1ca7dee1-5cc3-38d7-88ef-1caf164a6747"}
{"@timestamp":"2025-07-08T07:06:36.200Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T07:06:36.200Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T07:06:36.204Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T07:06:36.262Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$632d664] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.262Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$bf651b94] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.318Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$9efe8ef3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.363Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a93af6ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.396Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.identityverify.config.RedisConfig$$EnhancerBySpringCGLIB$$8fa9d59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.424Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.identityverify.config.AppConfig$$EnhancerBySpringCGLIB$$3436ba7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.464Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$142cb1fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.493Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.525Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c554f9cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:06:36.770Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T07:06:37.918Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T07:06:37.926Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T07:06:38.527Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksBmxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsFaceFeatureUtils': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'HsFaceFeature.HsFaceFeatureCreatorPath' in value \"${HsFaceFeature.HsFaceFeatureCreatorPath}\""}
{"@timestamp":"2025-07-08T07:06:38.532Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.b.a.l.ConditionEvaluationReportLoggingListener","rest":"\r\n\r\nError starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled."}
{"@timestamp":"2025-07-08T07:06:38.535Z","severity":"ERROR","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-08T07:06:38.538Z","severity":"ERROR","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"49508","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@156b88f5] to prepare test instance [com.xcwlkj.identityverify.service.impl.SjwjfwsccsServiceTest@729d991e]"}
{"@timestamp":"2025-07-08T07:32:28.956Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f8d04db1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:29.471Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T07:32:29.771Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-08T07:32:29.782Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"c.x.i.s.i.JyCommonPlatformServiceImplTest","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-08T07:32:32.513Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T07:32:32.517Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T07:32:32.636Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 98ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T07:32:32.792Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T07:32:32.930Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T07:32:33.319Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=7788f438-c606-3dc9-a02e-279227c258ac"}
{"@timestamp":"2025-07-08T07:32:33.623Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T07:32:33.632Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T07:32:33.644Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T07:32:33.724Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$39ae2a4a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:33.725Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$f2e06f7a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:33.850Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$d279e2d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:34.136Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$dcb64ab4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:34.188Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$47a805e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:34.232Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:34.307Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f8d04db1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T07:32:37.002Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T07:32:37.082Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T07:32:37.777Z","severity":"WARN","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-08T07:32:37.789Z","severity":"INFO","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.b.a.l.ConditionEvaluationReportLoggingListener","rest":"\r\n\r\nError starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled."}
{"@timestamp":"2025-07-08T07:32:38.050Z","severity":"ERROR","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\nThe following candidates were found but could not be injected:\r\n\t- Bean method 'redisUtilAdapter' in 'AppConfig' not loaded because @ConditionalOnProperty (spring.application.name=identityverify-service) found different value in property 'spring.application.name'\r\n\r\n\r\nAction:\r\n\r\nConsider revisiting the entries above or defining a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' in your configuration.\r\n"}
{"@timestamp":"2025-07-08T07:32:38.055Z","severity":"ERROR","service":"identityverify-service","trace":"","span":"","parent":"","exportable":"","pid":"14524","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6a2b953e] to prepare test instance [com.xcwlkj.identityverify.service.impl.JyCommonPlatformServiceImplTest@262f1580]"}
