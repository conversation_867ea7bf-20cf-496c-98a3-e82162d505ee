/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.identityverify.mapper.JySysDictMapper;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.HbTHConneTestDo;
import com.xcwlkj.identityverify.model.domain.JyCommonPlatform;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.dos.JsJFConneTestDo;
import com.xcwlkj.identityverify.model.dto.sysconfig.*;
import com.xcwlkj.identityverify.model.vo.sysconfig.*;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.HbTHService;
import com.xcwlkj.identityverify.third.superior.http.service.JsJFService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.mapper.JyCommonPlatformMapper;
import com.xcwlkj.identityverify.service.JyCommonPlatformService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 上级平台配置表服务
 * <AUTHOR>
 * @version $Id: JyCommonPlatformServiceImpl.java, v 0.1 2023年12月22日 14时06分 xcwlkj.com Exp $
 */
@Service("jyCommonPlatformService")
@Slf4j
public class JyCommonPlatformServiceImpl extends BaseServiceImpl<JyCommonPlatformMapper, JyCommonPlatform>  implements JyCommonPlatformService  {

    @Resource
    private JyCommonPlatformMapper modelMapper;
    @Resource
    private JySysDictService jySysDictService;
    @Resource
    private JySysDictMapper jySysDictMapper;
    @Resource
    private HbTHService hbTHService;
    @Resource
    private JsJFService jsJFService;
    @Resource
    private ProvincePlatformClient provincePlatformClient;


    @Override
    public void ptpzxxtjbj(PtpzxxtjDTO dto) {
        JyCommonPlatform jyCommonPlatform = new JyCommonPlatform();
        BeanUtil.copyProperties(dto,jyCommonPlatform);
        if (StringUtil.isBlank(dto.getId())){
            String id = IdGenerateUtil.generateId();
            jyCommonPlatform.setId(id);
            jyCommonPlatform.setCreateTime(new Date());
            jyCommonPlatform.setUpdateTime(new Date());
            modelMapper.insertSelective(jyCommonPlatform);

            // 生成默认的上级平台参数
            PtpzcscxDTO ptpzcscxDTO = new PtpzcscxDTO();
            ptpzcscxDTO.setType(dto.getPlatType());
            PtpzcscxVO ptpzcscx = jySysDictService.ptpzcscx(ptpzcscxDTO);
            List<PlatParamsItemVO> platParams = ptpzcscx.getPlatParams();
            if (!CollectionUtils.isEmpty(platParams)){
                List<JySysDict> upperPlatDock = platParams.stream().map(platParamsItemVO -> {
                    JySysDict jySysDict = new JySysDict();
                    String generateId = IdGenerateUtil.generateId();
                    jySysDict.setId(generateId);
                    jySysDict.setTCode(dto.getPlatType() + "_" + platParamsItemVO.getParamName());
                    jySysDict.setTName(platParamsItemVO.getParamNameDesc());
                    jySysDict.setTType("upperPlatDock");
                    jySysDict.setTCatalog("upperPlat");
                    
                    // 如果是恒生平台的文件服务器配置参数，设置对应的值
                    if ("HISOME".equals(dto.getPlatType())) {
                        String paramValue = getHisomeFileServerConfigValue(dto, platParamsItemVO.getParamName());
                        jySysDict.setTValue(paramValue != null ? paramValue : "");
                    } else {
                        jySysDict.setTValue("");
                    }
                    
                    jySysDict.setCreateTime(new Date());
                    jySysDict.setUpdateTime(new Date());
                    return jySysDict;
                }).collect(Collectors.toList());
                jySysDictMapper.insertListSelective(upperPlatDock);
            }
            
            // 如果是恒生平台，额外处理文件服务器配置的更新
            if ("HISOME".equals(dto.getPlatType())) {
                updateHisomeFileServerConfig(dto);
            }
        }else {
            jyCommonPlatform.setUpdateTime(new Date());
            modelMapper.updateByPrimaryKeySelective(jyCommonPlatform);
            
            // 如果是恒生平台，更新文件服务器配置
            if ("HISOME".equals(dto.getPlatType())) {
                updateHisomeFileServerConfig(dto);
            }
        }
    }

    @Override
    public PtpzxxlbVO ptpzxxlb(PtpzxxlbDTO dto) {
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        Example example = new Example(JyCommonPlatform.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotBlank(dto.getPlatType())){
            criteria.andEqualTo("platType",dto.getPlatType());
        }
        if (StringUtil.isNotBlank(dto.getServerIp())){
            criteria.andLike("serverIp",dto.getServerIp());
        }
        if (StringUtil.isNotBlank(dto.getServerPort())){
            criteria.andEqualTo("serverPort",dto.getServerPort());
        }
        if (StringUtil.isNotBlank(dto.getProxyIp())){
            criteria.andLike("proxyIp",dto.getProxyIp());
        }
        if (StringUtil.isNotBlank(dto.getProxyPort())){
            criteria.andEqualTo("proxyPort",dto.getProxyPort());
        }
        if (StringUtil.isNotBlank(dto.getAppId())){
            criteria.andEqualTo("appId",dto.getAppId());
        }
        List<JyCommonPlatform> jyCommonPlatforms = modelMapper.selectByExample(example);
        PageInfo<JyCommonPlatform> pageInfo = new PageInfo<>(jyCommonPlatforms);
        PtpzxxlbVO vo = new PtpzxxlbVO();
        List<PtpzxxlbItemVO> collect = jyCommonPlatforms.stream().map(jyCommonPlatform -> {
            PtpzxxlbItemVO itemVO = new PtpzxxlbItemVO();
            BeanUtil.copyProperties(jyCommonPlatform, itemVO);
            return itemVO;
        }).collect(Collectors.toList());
        vo.setPtpzxxlb(collect);
        vo.setTotalNums((int) pageInfo.getTotal());
        return vo;
    }

    @Override
    public PtpzxxxqVO ptpzxxxq(PtpzxxxqDTO dto) {
        JyCommonPlatform jyCommonPlatform = modelMapper.selectByPrimaryKey(dto.getId());
        PtpzxxxqVO vo = new PtpzxxxqVO();
        BeanUtil.copyProperties(jyCommonPlatform,vo);
        JySysDict jySysDict = jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
        if(jySysDict != null){
            vo.setDefaultPlat(jySysDict.getTValue());
        }
        
        // 如果是恒生平台，从jy_sys_dict表中读取文件服务器配置
        if ("HISOME".equals(jyCommonPlatform.getPlatType())) {
            loadHisomeFileServerConfig(vo);
        }
        
        return vo;
    }

    @Override
    public void ptpzxxsc(PtpzxxscDTO dto) {
        List<String> ids = dto.getIds();
        Example example = new Example(JyCommonPlatform.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        
        List<JyCommonPlatform> jyCommonPlatforms = modelMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(jyCommonPlatforms)){
            return;
        }
        List<String> tCodes = new ArrayList<>();
        jyCommonPlatforms.forEach(item->{
            PtpzcscxDTO ptpzcscxDTO = new PtpzcscxDTO();
            ptpzcscxDTO.setType(item.getPlatType());
            // 此查询使用枚举类，不经过数据库
            PtpzcscxVO ptpzcscx = jySysDictService.ptpzcscx(ptpzcscxDTO);
            List<PlatParamsItemVO> platParams = ptpzcscx.getPlatParams();
            if (!CollectionUtils.isEmpty(platParams)){
                List<String> collect = platParams.stream()
                        .map(platParamsItemVO -> item.getPlatType() + "_" + platParamsItemVO.getParamName())
                        .collect(Collectors.toList());
                tCodes.addAll(collect);
            }
        });
        jySysDictService.removeByTCode(tCodes);

        modelMapper.deleteByExample(example);
    }

    @Override
    public PtcsxxcxVO ptcsxxcx(String type) {
        PtpzcscxDTO ptpzcscxDTO = new PtpzcscxDTO();
        ptpzcscxDTO.setType(type);
        PtpzcscxVO ptpzcscx = jySysDictService.ptpzcscx(ptpzcscxDTO);
        List<PlatParamsItemVO> platParams = ptpzcscx.getPlatParams();
        List<String> tCodes = new ArrayList<>();
        Map<String,PlatParamsItemVO> tCodeParamMap = new HashMap<>();
        for (PlatParamsItemVO platParam : platParams) {
            String tCode = type + "_" + platParam.getParamName();
            tCodes.add(tCode);
            tCodeParamMap.put(tCode,platParam);
        }
        Example example = new Example(JySysDict.class);
        example.createCriteria().andIn("tCode",tCodes);
        List<JySysDict> jySysDicts = jySysDictMapper.selectByExample(example);
        PtcsxxcxVO vo = new PtcsxxcxVO();
        List<PtcslbItemVO> collect = jySysDicts.stream().map(jySysDict -> {
            PtcslbItemVO itemVO = new PtcslbItemVO();
            PlatParamsItemVO platParam = tCodeParamMap.get(jySysDict.getTCode());
            itemVO.setCode(platParam.getParamName());
            itemVO.setName(platParam.getParamNameDesc());
            itemVO.setValue(jySysDict.getTValue());
            return itemVO;
        }).collect(Collectors.toList());
        vo.setPtcslb(collect);
        return vo;
    }

    @Override
    public PtpzljcsVO ptpzljcs(PtpzljcsDTO dto) {
        String platType = dto.getPlatType();
        PtpzljcsVO ptpzljcsVO = new PtpzljcsVO();
        ptpzljcsVO.setResult("false");
        ptpzljcsVO.setMessage("连接失败，请检查配置信息");
        SuperiorPlatEnum superiorPlatEnum = null;
        for (SuperiorPlatEnum platTypeEnum : SuperiorPlatEnum.values()) {
            if (platTypeEnum.getCode().equals(platType)) {
                superiorPlatEnum = platTypeEnum;
            }
        }
        try {
            switch (superiorPlatEnum){
                case HB_TH:
                    HbTHConneTestDo hbTHConneTestDo = hbTHService.GetConnectionTest();
                    if (hbTHConneTestDo != null){
                       if(hbTHConneTestDo.getSuccess()){
                           ptpzljcsVO.setResult("true");
                           ptpzljcsVO.setMessage("连接成功");
                       }else{
                           ptpzljcsVO.setResult("false");
                           ptpzljcsVO.setMessage(hbTHConneTestDo.getMessage());
                       }
                    }
                    break;
                case JS_JF:
                    JsJFConneTestDo jsJFConneTestDo = jsJFService.GetConnectionTest();
                    if (jsJFConneTestDo != null){
                        if(jsJFConneTestDo.getResult()){
                            ptpzljcsVO.setResult("true");
                            ptpzljcsVO.setMessage("连接成功");
                        }else{
                            ptpzljcsVO.setResult("false");
                            ptpzljcsVO.setMessage(jsJFConneTestDo.getMessage());
                        }
                    }
                    break;
                case HISOME:
                    provincePlatformClient.freshToken();
                    ptpzljcsVO.setResult("true");
                    ptpzljcsVO.setMessage("连接成功");
                default:
                    break;
            }
        } catch (Exception e) {
            ptpzljcsVO.setMessage("网络连接错误！请检查ip或端口配置");
            log.error("连接测试异常",e);
        } finally {
        }
        return ptpzljcsVO;
    }
    
    /**
     * 获取恒生平台文件服务器配置参数值
     */
    private String getHisomeFileServerConfigValue(PtpzxxtjDTO dto, String paramName) {
        switch (paramName) {
            case "fileServerUrl":
                return dto.getFileServerUrl();
            case "fileServerChannel":
                return dto.getFileServerChannel();
            case "documentService":
                return dto.getDocumentService();
            case "fileServiceAppId":
                return dto.getFileServiceAppId();
            case "fileServiceAppSecret":
                return dto.getFileServiceAppSecret();
            default:
                return null;
        }
    }
    
    /**
     * 更新恒生平台文件服务器配置
     * 如果记录不存在则创建，存在则更新
     */
    private void updateHisomeFileServerConfig(PtpzxxtjDTO dto) {
        // 创建或更新文件服务器地址
        jySysDictService.createOrUpdateSysDict(
            "HISOME_fileServerUrl", 
            "文件服务器地址", 
            dto.getFileServerUrl(), 
            "upperPlatDock", 
            "upperPlat"
        );
        
        // 创建或更新文件服务渠道
        jySysDictService.createOrUpdateSysDict(
            "HISOME_fileServerChannel", 
            "文件服务渠道", 
            dto.getFileServerChannel(), 
            "upperPlatDock", 
            "upperPlat"
        );
        
        // 创建或更新文档服务
        jySysDictService.createOrUpdateSysDict(
            "HISOME_documentService", 
            "文档服务", 
            dto.getDocumentService(), 
            "upperPlatDock", 
            "upperPlat"
        );
        
        // 创建或更新文件服务appId
        jySysDictService.createOrUpdateSysDict(
            "HISOME_fileServiceAppId", 
            "文件服务appId", 
            dto.getFileServiceAppId(), 
            "upperPlatDock", 
            "upperPlat"
        );
        
        // 创建或更新文件服务appSecret
        jySysDictService.createOrUpdateSysDict(
            "HISOME_fileServiceAppSecret", 
            "文件服务appSecret", 
            dto.getFileServiceAppSecret(), 
            "upperPlatDock", 
            "upperPlat"
        );
    }
    
    /**
     * 加载恒生平台文件服务器配置
     * 处理记录不存在的情况，返回空值而不是null
     */
    private void loadHisomeFileServerConfig(PtpzxxxqVO vo) {
        // 读取文件服务器地址
        JySysDict fileServerUrl = jySysDictService.queryConfigValueByKey("HISOME_fileServerUrl");
        vo.setFileServerUrl(fileServerUrl != null ? fileServerUrl.getTValue() : "");
        
        // 读取文件服务渠道
        JySysDict fileServerChannel = jySysDictService.queryConfigValueByKey("HISOME_fileServerChannel");
        vo.setFileServerChannel(fileServerChannel != null ? fileServerChannel.getTValue() : "");
        
        // 读取文档服务
        JySysDict documentService = jySysDictService.queryConfigValueByKey("HISOME_documentService");
        vo.setDocumentService(documentService != null ? documentService.getTValue() : "");
        
        // 读取文件服务appId
        JySysDict fileServiceAppId = jySysDictService.queryConfigValueByKey("HISOME_fileServiceAppId");
        vo.setFileServiceAppId(fileServiceAppId != null ? fileServiceAppId.getTValue() : "");
        
        // 读取文件服务appSecret
        JySysDict fileServiceAppSecret = jySysDictService.queryConfigValueByKey("HISOME_fileServiceAppSecret");
        vo.setFileServiceAppSecret(fileServiceAppSecret != null ? fileServiceAppSecret.getTValue() : "");
    }
}