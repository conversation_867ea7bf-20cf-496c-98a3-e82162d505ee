package com.xcwlkj.identityverify.third.superior.http.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HisomeUriEnum {
    GET_TOKEN("/manager/getConnToken", "获取token"),
    GET_ACCESS_TOKEN("/manager/secret/getAccessToken", "获取access token"),
    GET_EXAM_PLAN("/ksrc/examInfo/getExamPlan/v1","获取考试计划"),
    GET_EXAM_ARRG_INFO("/ksrc/examInfo/getExamArrgInfo/v2","获取文件地址"),
    GET_EXAM_PLAN_OLD("/sfhy/v1/getExamPlan","获取考试计划(老版本)"),
    GET_EXAM_ARRG_INFO_OLD("/sfhy/v1/getExamArrgInfo","获取文件地址(老版本)"),
    SET_VALIDATE_INFO("/sfhy/v1/setValidateInfo", "下级实时上传核验数据JSON"),
    SET_VALIDATE_STU_ENTER("/sfhy/v1/setValidateStuEnter", "考生入场状态上报"),
    SET_VALIDATE_INFO_NEW("/ksyw/revIdvData/setValidateInfo", "考生核验信息"),
    SET_VALIDATE_STU_ENTER_NEW("/ksyw/revIdvData/setValidateStuEnter", "考场入场人工（入场、缺考）"),
    SET_VALIDATE_PIC("/ksyw/revIdvData/setValidatePic", "考生照片上传"),
    PKG_DOWNLOAD_COMPLETE("/ksrc/examInfo/pkgDownloadComplete/v1","完成编排数据下载上报");

    private String url;

    private String name;
}
