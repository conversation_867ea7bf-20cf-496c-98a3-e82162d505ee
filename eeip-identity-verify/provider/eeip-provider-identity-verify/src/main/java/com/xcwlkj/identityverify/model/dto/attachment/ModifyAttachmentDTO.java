/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.attachment;

import com.xcwlkj.base.dto.BaseVo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务附件表
 * <AUTHOR>
 * @version $Id: ModifyAttachmentDTO.java, v 0.1 2018年12月10日 上午10:15:14 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ModifyAttachmentDTO extends BaseVo {
    /** 序列id */
    private static final long serialVersionUID = 1L;

    /** 上传附件的相关业务流水号 */
    private String            refNo;
    /** 文件服务器根目录(七牛云) */
    private String            bucketName;
    /** 原始文件名称 */
    private String            originalName;
    /** 新文件名称 */
    private String            newFileName;
    /** 附件存储相对路径 */
    private String            path;
    /** 附件类型 */
    private String            type;
    /** 备注 */
    private String            description;

}
