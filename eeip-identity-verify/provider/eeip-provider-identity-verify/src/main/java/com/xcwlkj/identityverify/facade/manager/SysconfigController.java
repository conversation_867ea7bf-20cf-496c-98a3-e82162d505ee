/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.identityverify.model.dto.sysconfig.*;
import com.xcwlkj.identityverify.model.req.sysconfig.*;
import com.xcwlkj.identityverify.model.resp.sysconfig.*;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.sysconfig.*;
import com.xcwlkj.identityverify.service.CsXxjbxxService;
import com.xcwlkj.identityverify.service.JyCommonPlatformService;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.service.impl.AttachmentHandler;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.util.RequestUtil;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.common.DateTime;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Sysconfig控制层
 * <AUTHOR>
 * @version $Id: SysconfigController.java, v 0.1 2023年11月20日 11时31分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("SysconfigManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SysconfigController extends BaseController {

    @Resource
    private JySysDictService jySysDictService;
    @Resource
    private CsXxjbxxService csXxjbxxService;
    @Resource
    private JyCommonPlatformService jyCommonPlatformService;
    @Resource
    private AttachmentHandler attachmentHandler;

   /**
    * 上级平台（省平台）信息配置
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:sjptxxpz")
    @PostMapping(value = "/manager/identityverify/sysConfig/sjptxxpz")
    public Wrapper<SjptxxpzRespModel> sjptxxpz(@RequestBody SjptxxpzReqModel reqModel) {
		log.info("收到请求开始：[上级平台（省平台）信息配置][/manager/identityverify/sysConfig/sjptxxpz]reqModel:"+reqModel.toString());
		SjptxxpzDTO dto = new SjptxxpzDTO();
        dto.setUrl(reqModel.getUrl());
        dto.setUsername(reqModel.getUsername());
        dto.setPassword(reqModel.getPassword());
		jySysDictService.sjptxxpz(dto);
        SjptxxpzRespModel respModel = new SjptxxpzRespModel();

		log.info("处理请求结束：[上级平台（省平台）信息配置][/manager/identityverify/sysConfig/sjptxxpz]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取上级平台配置信息
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:hqsjptpzxx")
    @PostMapping(value = "/manager/identityverify/sysConfig/hqsjptpzxx")
    public Wrapper<HqsjptpzxxRespModel> hqsjptpzxx(@RequestBody HqsjptpzxxReqModel reqModel) {
		log.info("收到请求开始：[获取上级平台配置信息][/manager/identityverify/sysConfig/hqsjptpzxx]reqModel:"+reqModel.toString());

		HqsjptpzxxVO result = jySysDictService.hqsjptpzxx();
        HqsjptpzxxRespModel respModel = new HqsjptpzxxRespModel();
        respModel.setUrl(result.getUrl());
        respModel.setUsername(result.getUsername());
		log.info("处理请求结束：[获取上级平台配置信息][/manager/identityverify/sysConfig/hqsjptpzxx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 本校信息配置
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:bxxxpz")
    @PostMapping(value = "/manager/identityverify/sysConfig/bxxxpz")
    public Wrapper<BxxxpzRespModel> bxxxpz(@RequestBody BxxxpzReqModel reqModel) {
		log.info("收到请求开始：[本校信息配置][/manager/identityverify/sysConfig/bxxxpz]reqModel:"+reqModel.toString());
		BxxxpzDTO dto = new BxxxpzDTO();
        dto.setXxmc(reqModel.getXxmc());
        dto.setZzjgm(reqModel.getZzjgm());
        dto.setXxdz(reqModel.getXxdz());
        dto.setXzqhm(reqModel.getXzqhm());
		csXxjbxxService.bxxxpz(dto);
        BxxxpzRespModel respModel = new BxxxpzRespModel();

		log.info("处理请求结束：[本校信息配置][/manager/identityverify/sysConfig/bxxxpz]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取本校配置信息
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:hqbxpzxx")
    @PostMapping(value = "/manager/identityverify/sysConfig/hqbxpzxx")
    public Wrapper<HqbxpzxxRespModel> hqbxpzxx(@RequestBody HqbxpzxxReqModel reqModel) {
		log.info("收到请求开始：[获取本校配置信息][/manager/identityverify/sysConfig/hqbxpzxx]reqModel:"+reqModel.toString());
		HqbxpzxxDTO dto = new HqbxpzxxDTO();

		HqbxpzxxVO result = csXxjbxxService.hqbxpzxx(dto);
        HqbxpzxxRespModel respModel = new HqbxpzxxRespModel();
        respModel.setXxmc(result.getXxmc());
        respModel.setZzjgm(result.getZzjgm());
        respModel.setXxdz(result.getXxdz());
        respModel.setXzqhm(result.getXzqhm());
        respModel.setSbxlh(result.getSbxlh());
		log.info("处理请求结束：[获取本校配置信息][/manager/identityverify/sysConfig/hqbxpzxx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 设备上报系统配置
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/sysConfig/sbsbxtpz")
    public Wrapper<SbsbxtpzRespModel> sbsbxtpz(@RequestBody SbsbxtpzReqModel reqModel) {
		log.info("收到请求开始：[设备上报系统配置][/manager/identityverify/sysConfig/sbsbxtpz]reqModel:"+reqModel.toString());
		SbsbxtpzDTO dto = new SbsbxtpzDTO();
        dto.setTypes(reqModel.getTypes());
		jySysDictService.sbsbxtpz(dto);
        SbsbxtpzRespModel respModel = new SbsbxtpzRespModel();

		log.info("处理请求结束：[设备上报系统配置][/manager/identityverify/sysConfig/sbsbxtpz]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取设备上报系统配置
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/sysConfig/hqsbsbpzxx")
    public Wrapper<HqsbsbpzxxRespModel> hqsbsbpzxx(@RequestBody HqsbsbpzxxReqModel reqModel) {
		log.info("收到请求开始：[获取设备上报系统配置][/manager/identityverify/sysConfig/hqsbsbpzxx]reqModel:"+reqModel.toString());
		HqsbsbpzxxDTO dto = new HqsbsbpzxxDTO();

		HqsbsbpzxxVO result = jySysDictService.hqsbsbpzxx();
        HqsbsbpzxxRespModel respModel = new HqsbsbpzxxRespModel();
        respModel.setTypes(result.getTypes());
		log.info("处理请求结束：[获取设备上报系统配置][/manager/identityverify/sysConfig/hqsbsbpzxx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置信息添加/编辑
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzxxtjbj")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzxxtjbj")
    public Wrapper<PtpzxxtjRespModel> ptpzxxtj(@RequestBody PtpzxxtjReqModel reqModel) {
		log.info("收到请求开始：[平台配置信息添加/编辑][/manager/identityverify/sysConfig/ptpzxxtj]reqModel:"+reqModel.toString());
		PtpzxxtjDTO dto = new PtpzxxtjDTO();
        dto.setPlatType(reqModel.getPlatType());
        dto.setServerIp(reqModel.getServerIp());
        dto.setServerPort(reqModel.getServerPort());
        dto.setProxyIp(reqModel.getProxyIp());
        dto.setProxyPort(reqModel.getProxyPort());
        dto.setAppId(reqModel.getAppId());
        dto.setAppKey(reqModel.getAppKey());
        dto.setId(reqModel.getId());
        dto.setFileServerUrl(reqModel.getFileServerUrl());
        dto.setFileServerChannel(reqModel.getFileServerChannel());
        dto.setDocumentService(reqModel.getDocumentService());
        dto.setFileServiceAppId(reqModel.getFileServiceAppId());
        dto.setFileServiceAppSecret(reqModel.getFileServiceAppSecret());
		jyCommonPlatformService.ptpzxxtjbj(dto);
        PtpzxxtjRespModel respModel = new PtpzxxtjRespModel();

		log.info("处理请求结束：[平台配置信息添加/编辑][/manager/identityverify/sysConfig/ptpzxxtj]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置信息列表查询
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzxxlb")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzxxlb")
    public Wrapper<PtpzxxlbRespModel> ptpzxxlb(@RequestBody PtpzxxlbReqModel reqModel) {
		log.info("收到请求开始：[平台配置信息列表查询][/manager/identityverify/sysConfig/ptpzxxlb]reqModel:"+reqModel.toString());
		PtpzxxlbDTO dto = new PtpzxxlbDTO();
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        dto.setPlatType(reqModel.getPlatType());
        dto.setServerIp(reqModel.getServerIp());
        dto.setServerPort(reqModel.getServerPort());
        dto.setProxyIp(reqModel.getProxyIp());
        dto.setProxyPort(reqModel.getProxyPort());
        dto.setAppId(reqModel.getAppId());
		PtpzxxlbVO result = jyCommonPlatformService.ptpzxxlb(dto);
        PtpzxxlbRespModel respModel = new PtpzxxlbRespModel();
        respModel.setPtpzxxlb(result.getPtpzxxlb());
        respModel.setTotalNums(result.getTotalNums());
		log.info("处理请求结束：[平台配置信息列表查询][/manager/identityverify/sysConfig/ptpzxxlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置信息详情
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzxxxq")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzxxxq")
    public Wrapper<PtpzxxxqRespModel> ptpzxxxq(@RequestBody PtpzxxxqReqModel reqModel) {
		log.info("收到请求开始：[平台配置信息详情][/manager/identityverify/sysConfig/ptpzxxxq]reqModel:"+reqModel.toString());
		PtpzxxxqDTO dto = new PtpzxxxqDTO();
        dto.setId(reqModel.getId());
		PtpzxxxqVO result = jyCommonPlatformService.ptpzxxxq(dto);
        PtpzxxxqRespModel respModel = new PtpzxxxqRespModel();
        respModel.setId(result.getId());
        respModel.setPlatType(result.getPlatType());
        respModel.setServerIp(result.getServerIp());
        respModel.setServerPort(result.getServerPort());
        respModel.setProxyIp(result.getProxyIp());
        respModel.setProxyPort(result.getProxyPort());
        respModel.setAppId(result.getAppId());
        respModel.setAppKey(result.getAppKey());
        respModel.setDefaultPlat(result.getDefaultPlat());
        respModel.setFileServerUrl(result.getFileServerUrl());
        respModel.setFileServerChannel(result.getFileServerChannel());
        respModel.setDocumentService(result.getDocumentService());
        respModel.setFileServiceAppId(result.getFileServiceAppId());
        respModel.setFileServiceAppSecret(result.getFileServiceAppSecret());
		log.info("处理请求结束：[平台配置信息详情][/manager/identityverify/sysConfig/ptpzxxxq]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置信息删除
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzxxsc")
    public Wrapper<PtpzxxscRespModel> ptpzxxsc(@RequestBody PtpzxxscReqModel reqModel) {
		log.info("收到请求开始：[平台配置信息删除][/manager/identityverify/sysConfig/ptpzxxsc]reqModel:"+reqModel.toString());
		PtpzxxscDTO dto = new PtpzxxscDTO();
        dto.setIds(reqModel.getIds());
		jyCommonPlatformService.ptpzxxsc(dto);
        PtpzxxscRespModel respModel = new PtpzxxscRespModel();

		log.info("处理请求结束：[平台配置信息删除][/manager/identityverify/sysConfig/ptpzxxsc]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取其他平台的类型代码和名称列表
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptlxlbcx")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptlxlbcx")
    public Wrapper<PtlxlbcxRespModel> ptlxlbcx(@RequestBody PtlxlbcxReqModel reqModel) {
		log.info("收到请求开始：[获取其他平台的类型代码和名称列表][/manager/identityverify/sysConfig/ptlxlbcx]reqModel:"+reqModel.toString());
        List<PlatTypesItemVO> collect = Arrays.stream(SuperiorPlatEnum.values()).map(e -> {
            PlatTypesItemVO itemVO = new PlatTypesItemVO();
            itemVO.setCode(e.getCode());
            itemVO.setName(e.getName());
            return itemVO;
        }).collect(Collectors.toList());
        PtlxlbcxRespModel respModel = new PtlxlbcxRespModel();
        respModel.setPlatTypes(collect);
        log.info("处理请求结束：[获取其他平台的类型代码和名称列表][/manager/identityverify/sysConfig/ptlxlbcx]reqModel:"+reqModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 根据平台类型获取需要配置的参数
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzcscx")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzcscx")
    public Wrapper<PtpzcscxRespModel> ptpzcscx(@RequestBody PtpzcscxReqModel reqModel) {
		log.info("收到请求开始：[根据平台类型获取需要配置的参数][/manager/identityverify/sysConfig/ptpzcscx]reqModel:"+reqModel.toString());
		PtpzcscxDTO dto = new PtpzcscxDTO();
        dto.setType(reqModel.getType());
		PtpzcscxVO result = jySysDictService.ptpzcscx(dto);
        PtpzcscxRespModel respModel = new PtpzcscxRespModel();
        respModel.setPlatParams(result.getPlatParams());
		log.info("处理请求结束：[根据平台类型获取需要配置的参数][/manager/identityverify/sysConfig/ptpzcscx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置信息设置
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzxxsz")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzxxsz")
    public Wrapper<PtpzxxszRespModel> ptpzxxsz(@RequestBody PtpzxxszReqModel reqModel) {
		log.info("收到请求开始：[平台配置信息设置][/manager/identityverify/sysConfig/ptpzxxsz]reqModel:"+reqModel.toString());
		PtpzxxszDTO dto = new PtpzxxszDTO();
        dto.setParams(reqModel.getParams());
        dto.setPlatCode(reqModel.getPlatCode());
		jySysDictService.ptpzxxsz(dto);
        PtpzxxszRespModel respModel = new PtpzxxszRespModel();

		log.info("处理请求结束：[平台配置信息设置][/manager/identityverify/sysConfig/ptpzxxsz]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台参数信息查询
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptcsxxcx")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptcsxxcx")
    public Wrapper<PtcsxxcxRespModel> ptcsxxcx(@RequestBody PtcsxxcxReqModel reqModel) {
		log.info("收到请求开始：[平台参数信息查询][/manager/identityverify/sysConfig/ptcsxxcx]reqModel:"+reqModel.toString());
		PtcsxxcxDTO dto = new PtcsxxcxDTO();
        dto.setType(reqModel.getType());
		PtcsxxcxVO result = jyCommonPlatformService.ptcsxxcx(dto.getType());
        PtcsxxcxRespModel respModel = new PtcsxxcxRespModel();
        respModel.setPtcslb(result.getPtcslb());
		log.info("处理请求结束：[平台参数信息查询][/manager/identityverify/sysConfig/ptcsxxcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 平台配置连接测试
    * GetConnectionTest方法暂时不支持传参
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:ptpzljcs")
    @PostMapping(value = "/manager/identityverify/sysConfig/ptpzljcs")
    public Wrapper<PtpzljcsRespModel> ptpzljcs(@RequestBody PtpzljcsReqModel reqModel) {
		log.info("收到请求开始：[平台配置连接测试][/manager/identityverify/sysConfig/ptpzljcs]reqModel:"+reqModel.toString());
		PtpzljcsDTO dto = new PtpzljcsDTO();
        dto.setPlatType(reqModel.getPlatType());
        dto.setServerIp(reqModel.getServerIp());
        dto.setServerPort(reqModel.getServerPort());
        dto.setProxyIp(reqModel.getProxyIp());
        dto.setProxyPort(reqModel.getProxyPort());
        dto.setAppId(reqModel.getAppId());
        dto.setAppKey(reqModel.getAppKey());
		PtpzljcsVO result = jyCommonPlatformService.ptpzljcs(dto);
        PtpzljcsRespModel respModel = new PtpzljcsRespModel();
        respModel.setResult(result.getResult());
        respModel.setMessage(result.getMessage());
		log.info("处理请求结束：[平台配置连接测试][/manager/identityverify/sysConfig/ptpzljcs]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 设置默认上级平台
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:mrsjptsz")
    @PostMapping(value = "/manager/identityverify/sysConfig/mrsjptsz")
    public Wrapper<MrsjptszRespModel> mrsjptsz(@RequestBody MrsjptszReqModel reqModel) {
		log.info("收到请求开始：[设置默认上级平台][/manager/identityverify/sysConfig/mrsjptsz]reqModel:"+reqModel.toString());
		MrsjptszDTO dto = new MrsjptszDTO();
        dto.setMrpt(reqModel.getMrpt());
		jySysDictService.mrsjptsz(dto);
        MrsjptszRespModel respModel = new MrsjptszRespModel();

		log.info("处理请求结束：[设置默认上级平台][/manager/identityverify/sysConfig/mrsjptsz]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 文件服务参数配置查询
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:wjfwcspzcx")
    @PostMapping(value = "/manager/identityverify/sysConfig/wjfwcspzcx")
    public Wrapper<WjfwcspzcxRespModel> wjfwcspzcx(@RequestBody WjfwcspzcxReqModel reqModel) {
		log.info("收到请求开始：[文件服务参数配置查询][/manager/identityverify/sysConfig/wjfwcspzcx]reqModel:"+reqModel.toString());
		WjfwcspzcxDTO dto = new WjfwcspzcxDTO();
        WjfwcspzcxVO result = jySysDictService.wjfwcspzcx(dto);
        WjfwcspzcxRespModel respModel = new WjfwcspzcxRespModel();
        respModel.setWjfwcspzcxList(result.getWjfwcspzcxList());
		log.info("处理请求结束：[文件服务参数配置查询][/manager/identityverify/sysConfig/wjfwcspzcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 文件服务参数配置保存
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:wjfwcspzbc")
    @PostMapping(value = "/manager/identityverify/sysConfig/wjfwcspzbc")
    public Wrapper<WjfwcspzbcRespModel> wjfwcspzbc(@RequestBody WjfwcspzbcReqModel reqModel) {
		log.info("收到请求开始：[文件服务参数配置保存][/manager/identityverify/sysConfig/wjfwcspzbc]reqModel:"+reqModel.toString());
		WjfwcspzbcDTO dto = new WjfwcspzbcDTO();
        dto.setWjfwcspzbcList(reqModel.getWjfwcspzbcList());
        jySysDictService.wjfwcspzbc(dto);
        WjfwcspzbcRespModel respModel = new WjfwcspzbcRespModel();

		log.info("处理请求结束：[文件服务参数配置保存][/manager/identityverify/sysConfig/wjfwcspzbc]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
	@Permission("sysConfig:wjfwcsljcs")
    @PostMapping(value = "/manager/identityverify/sysConfig/wjfwcsljcs")
    public Wrapper<WjfwcsljcsRespModel> wjfwcsljcs(@RequestBody WjfwcsljcsReqModel reqModel) {
        log.info("收到请求开始：[文件服务参数连接测试][/manager/identityverify/sysConfig/wjfwcsljcs]reqModel:"+reqModel.toString());
        WjfwcsljcsDTO dto = new WjfwcsljcsDTO();
        BeanUtil.copyProperties(reqModel, dto);
        WjfwcsljcsRespModel respModel = new WjfwcsljcsRespModel();
        XcSsoUser loginUser = RequestUtil.getLoginUser();
        String testFilepath = "/tmp/identityverify/testdfs.txt";
        File file = new File(testFilepath);
        if (!file.exists()) {
            file.getParentFile().mkdirs();
        }
        // 写入测试内容
        try (FileWriter writer = new FileWriter(file)){
            writer.write("测试文件上传");
            writer.flush();
            DateTime expireDate = DateUtil.offsetMinute(new Date(), 5);
            XcDfsClient.getToken();
            UploadAttachmentReturnUrlVO uploadAttachmentReturnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(testFilepath, expireDate, loginUser);
            respModel.setStatus("0");
            respModel.setMessage("上传测试文件成功");
        } catch (IOException e) {
            log.error(e.toString());
            respModel.setStatus("1");
            respModel.setMessage("创建测试文件失败");
        } catch (Exception e){
            log.error(e.toString());
            respModel.setStatus("1");
            respModel.setMessage("上传测试文件失败");
        } finally {
            file.delete();
        }
        log.info("处理请求结束：[文件服务参数连接测试][/manager/identityverify/sysConfig/wjfwcsljcs]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * mqtt配置查询
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:mqttpzcx")
    @PostMapping(value = "/manager/identityverify/sysConfig/mqttpzcx")
    public Wrapper<MqttpzcxRespModel> mqttpzcx(@RequestBody MqttpzcxReqModel reqModel) {
		log.info("收到请求开始：[mqtt配置查询][/manager/identityverify/sysConfig/mqttpzcx]reqModel:"+reqModel.toString());
		MqttpzcxDTO dto = new MqttpzcxDTO();
		 MqttpzcxVO result = jySysDictService.mqttpzcx(dto);
        MqttpzcxRespModel respModel = new MqttpzcxRespModel();
        respModel.setMqttpzcxList(result.getMqttpzcxList());
		log.info("处理请求结束：[mqtt配置查询][/manager/identityverify/sysConfig/mqttpzcx]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * mqtt配置保存
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:mqttpzbc")
    @PostMapping(value = "/manager/identityverify/sysConfig/mqttpzbc")
    public Wrapper<MqttpzbcRespModel> mqttpzbc(@RequestBody MqttpzbcReqModel reqModel) {
		log.info("收到请求开始：[mqtt配置保存][/manager/identityverify/sysConfig/mqttpzbc]reqModel:"+reqModel.toString());
		MqttpzbcDTO dto = new MqttpzbcDTO();
        dto.setMqttpzbcList(reqModel.getMqttpzbcList());
        jySysDictService.mqttpzbc(dto);
        MqttpzbcRespModel respModel = new MqttpzbcRespModel();

		log.info("处理请求结束：[mqtt配置保存][/manager/identityverify/sysConfig/mqttpzbc]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * mqtt配置连接测试
    * @param reqModel
    * @return
    */
	@Permission("sysConfig:mqttpzljcs")
    @PostMapping(value = "/manager/identityverify/sysConfig/mqttpzljcs")
    public Wrapper<MqttpzljcsRespModel> mqttpzljcs(@RequestBody MqttpzljcsReqModel reqModel) {
		log.info("收到请求开始：[mqtt配置连接测试][/manager/identityverify/sysConfig/mqttpzljcs]reqModel:"+reqModel.toString());
		MqttpzljcsDTO dto = new MqttpzljcsDTO();
        dto.setMqttIp(reqModel.getMqttIp());
        dto.setMqttPort(reqModel.getMqttPort());
        dto.setMqttUsername(reqModel.getMqttUsername());
        dto.setMqttPassword(reqModel.getMqttPassword());

        MqttpzljcsVO result = jySysDictService.mqttpzljcs(dto);
        MqttpzljcsRespModel respModel = new MqttpzljcsRespModel();
        respModel.setStatus(result.getStatus());
        respModel.setMessage(result.getMessage());
		log.info("处理请求结束：[mqtt配置连接测试][/manager/identityverify/sysConfig/mqttpzljcs]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

	@Permission("sysConfig:jrtbsbsjdql")
    @PostMapping(value = "/manager/identityverify/sysConfig/jrtbsbsjdql")
    public Wrapper<Void> jrtbsbsjdql(@RequestBody JrtbsbsjdqlReqModel reqModel) {
        log.info("收到请求开始：[接入同步设备时间点清理][/manager/identityverify/sysConfig/jrtbsbsjdql]reqModel:"+reqModel.toString());
        jySysDictService.jrtbsbsjdql();

        log.info("处理请求结束：[接入同步设备时间点清理][/manager/identityverify/sysConfig/jrtbsbsjdql]reqModel:"+reqModel.toString()
            );
        return WrapMapper.ok();
    }

    /**
     * 上级文件服务上传测试
     * @param reqModel
     * @return
     */
    @Permission("sysConfig:sjwjfwsccs")
    @PostMapping(value = "/manager/identityverify/sysConfig/sjwjfwsccs")
    public Wrapper<SjwjfwsccsRespModel> sjwjfwsccs(@RequestBody SjwjfwsccsReqModel reqModel) {
        log.info("收到请求开始：[上级文件服务上传测试][/manager/identityverify/sysConfig/sjwjfwsccs]reqModel:"+reqModel.toString());
        
        SjwjfwsccsDTO dto = new SjwjfwsccsDTO();
        BeanUtil.copyProperties(reqModel, dto);
        
        SjwjfwsccsVO result = jySysDictService.sjwjfwsccs(dto);
        
        SjwjfwsccsRespModel respModel = new SjwjfwsccsRespModel();
        BeanUtil.copyProperties(result, respModel);
        
        log.info("处理请求结束：[上级文件服务上传测试][/manager/identityverify/sysConfig/sjwjfwsccs]reqModel:"+reqModel.toString()
            +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

}