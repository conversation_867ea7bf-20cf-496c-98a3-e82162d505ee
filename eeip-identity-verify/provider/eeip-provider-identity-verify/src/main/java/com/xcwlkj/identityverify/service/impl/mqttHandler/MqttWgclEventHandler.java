package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.xcwlkj.identityverify.service.impl.WgclEventDbHandler;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 处理违规考生功能
 */
@Slf4j
@Service
public class MqttWgclEventHandler extends MqttBaseEventHandler {

    @Resource
    private WgclEventDbHandler wgclEventDbHandler;

    @Override
    public HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        HanleResult hanleResult = new HanleResult();
        Object respObj = null;
        boolean needToSendResp = true;
        switch (devEventEnum) {
            case SCH_WJ_REGISTER:
                // 处理mqtt违规登记消息
                WgksDjReq wgksDjReq = (WgksDjReq) req;
                respObj = wgclEventDbHandler.wgdj(wgksDjReq);
                break;
            case SCH_WJ_QUERY:
                // 处理mqtt违规列表查询消息
                WgksLbReq wgksLbReq = (WgksLbReq) req;
                respObj = wgclEventDbHandler.wglb(wgksLbReq);
                break;
            case SCH_WJ_HANDLE:
                // 处理mqtt违规处理消息
                WgksClReq wgksClReq = (WgksClReq) req;
                respObj = wgclEventDbHandler.wgcl(wgksClReq);
                break;
            case SCH_WJ_REPORT:
                // 处理mqtt违规上报消息
                WgksSbReq wgksSbReq = (WgksSbReq) req;
                respObj = wgclEventDbHandler.wgsb(wgksSbReq);
                break;
            default:
                break;
        }
        hanleResult.needToSendResp = needToSendResp;
        hanleResult.respObj = respObj;
        return hanleResult;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.WGKS_EVENT_HANDLER;
    }
}
