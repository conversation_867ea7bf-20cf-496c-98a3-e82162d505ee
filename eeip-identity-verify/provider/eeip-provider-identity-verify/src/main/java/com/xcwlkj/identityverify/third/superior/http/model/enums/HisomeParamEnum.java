package com.xcwlkj.identityverify.third.superior.http.model.enums;

public enum HisomeParamEnum {
    USER_NAME("username", "省端账号名称"),
    USER_PWD("userpwd", "省端账号密码"),
    SUB_DEV_TYPE("subDevType", "上报设备类型"),
    URL("url", "hisome平台url"),
    FILE_SERVER_URL("fileServerUrl", "文件服务器地址"),
    FILE_SERVER_CHANNEL("fileServerChannel", "文件服务渠道"),
    DOCUMENT_SERVICE("documentService", "文档服务"),
    FILE_SERVICE_APP_ID("fileServiceAppId", "文件服务appId"),
    FILE_SERVICE_APP_SECRET("fileServiceAppSecret", "文件服务appSecret");

    private final String code;

    private final String name;

    HisomeParamEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
