/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.mapper;

import com.xcwlkj.identityverify.config.commonMapper.CustomMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import com.xcwlkj.identityverify.model.domain.JxPkgTask;



/**
 * 教学数据打包任务数据库操作
 * <AUTHOR>
 * @version $Id: InitJxPkgTaskMapper.java, v 0.1 2023年11月28日 13时40分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JxPkgTaskMapper extends CustomMapper<JxPkgTask> {


}
