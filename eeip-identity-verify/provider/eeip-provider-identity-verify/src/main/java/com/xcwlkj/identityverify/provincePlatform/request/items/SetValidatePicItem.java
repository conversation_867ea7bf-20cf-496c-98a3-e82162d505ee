package com.xcwlkj.identityverify.provincePlatform.request.items;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetValidatePicItem implements Serializable {

    /** 准考证号 */
    private String zkzh;

    /** 考点代码 */
    private String kddm;

    /** 考场号 */
    private String kch;

    /** 上传上级平台文件服务器现场照片id */
    private String xczp;

    /** 上传上级平台文件服务器身份证照片id */
    private String sfzzp;
}
