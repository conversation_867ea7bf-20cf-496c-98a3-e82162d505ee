/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.identityverify.mapper.CsJsjbxxMapper;
import com.xcwlkj.identityverify.mapper.JxDistributeStatusMapper;
import com.xcwlkj.identityverify.mapper.SbSbcsgxMapper;
import com.xcwlkj.identityverify.model.domain.JxDistributeStatus;
import com.xcwlkj.identityverify.model.dos.JxJsxxDo;
import com.xcwlkj.identityverify.model.dos.JxsbxxDo;
import com.xcwlkj.identityverify.model.dto.jxhy.JxsjxftjWwcDTO;
import com.xcwlkj.identityverify.model.dto.jxhy.JxsjxftjlbDTO;
import com.xcwlkj.identityverify.model.dto.jxhy.JxsjxfztlbDTO;
import com.xcwlkj.identityverify.model.dto.jxhy.JxsjxfztxqDTO;
import com.xcwlkj.identityverify.model.enums.DataDistributeStatusEnum;
import com.xcwlkj.identityverify.model.enums.JcsblxEnum;
import com.xcwlkj.identityverify.model.vo.jxhy.*;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxlbItemVO;
import com.xcwlkj.identityverify.service.JxDistributeStatusService;
import com.xcwlkj.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 教学数据下发状态服务
 *
 * <AUTHOR>
 * @version $Id: JxDistributeStatusServiceImpl.java, v 0.1 2023年11月28日 10时07分 xcwlkj.com Exp $
 */
@Service("jxDistributeStatusService")
public class JxDistributeStatusServiceImpl extends BaseServiceImpl<JxDistributeStatusMapper, JxDistributeStatus> implements JxDistributeStatusService {

    @Resource
    private JxDistributeStatusMapper modelMapper;
    @Resource
    private CsJsjbxxMapper csJsjbxxMapper;
    @Resource
    private SbSbcsgxMapper sbcsgxMapper;


    @Override
    public JxsjxfztlbVO jxsjxfztlb(JxsjxfztlbDTO dto) {
        List<JsxxlbItemVO> jsxxlbcx = csJsjbxxMapper.jsxxlbcx(dto.getCsmc(), null, null, null);
        List<String> jshList = jsxxlbcx.stream().map(JsxxlbItemVO::getJsh).collect(Collectors.toList());
        Map<String, String> csMap = jsxxlbcx.stream().collect(Collectors.toMap(JsxxlbItemVO::getJsh, JsxxlbItemVO::getCsmc));

        List<JxsbxxDo> jxsbxxlb = sbcsgxMapper.jxsbxxlb(null, null, null);
        Map<String, JxsbxxDo> sbMap = jxsbxxlb.stream().collect(Collectors.toMap(JxsbxxDo::getJsh, Function.identity(), (oleV, newV) -> newV));

        Example example = new Example(JxDistributeStatus.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("jxTaskId", dto.getJxrwbh());
        if (StringUtils.isNotBlank(dto.getCsmc())) {
            criteria.andIn("jsh", jshList);
        }
        if (StringUtils.equals(dto.getStatus(), "-1")) {
            criteria.andNotEqualTo("jxinfPkgStatus", DataDistributeStatusEnum.Done.getCode());
        } else if (StringUtils.equals(dto.getStatus(), "1")) {
            criteria.andEqualTo("jxinfPkgStatus", DataDistributeStatusEnum.Done.getCode());
        }
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<JxDistributeStatus> jxDistributeStatuses = modelMapper.selectByExample(example);

        ArrayList<JxsjxfztItemVO> jxsjxfztItemVOS = new ArrayList<>();
        for (JxDistributeStatus jxDistributeStatus : jxDistributeStatuses) {
            String jsh = jxDistributeStatus.getJsh();
            String sblx;
            String sbzt;
            if (sbMap.get(jsh) != null) {
                JxsbxxDo jxsbxxDo = sbMap.get(jsh);
                sblx = JcsblxEnum.getDesciptionByMsg(jxsbxxDo.getSblx());
                sbzt = jxsbxxDo.getZxzt();
            } else {
                sblx = "-";
                sbzt = "-1";
            }

            JxsjxfztItemVO jxsjxfztItemVO = new JxsjxfztItemVO();
            jxsjxfztItemVO.setId(jxDistributeStatus.getId());
            jxsjxfztItemVO.setCsbh(jsh);
            jxsjxfztItemVO.setCsmc(csMap.getOrDefault(jsh, "-"));
            jxsjxfztItemVO.setSblx(sblx);
            jxsjxfztItemVO.setSbzt(sbzt);

            int jxinfPkgStatus = Optional.ofNullable(jxDistributeStatus.getJxinfPkgStatus()).orElse(0);
            ArrayList<Integer> statusArr = new ArrayList<>();
            statusArr.add(jxinfPkgStatus);
            int xfzs = 1;
            int yxfs = statusArr.stream().filter(x -> x == 10).collect(Collectors.toList()).size();
            jxsjxfztItemVO.setXfzs(xfzs);
            jxsjxfztItemVO.setYxfs(yxfs);
            jxsjxfztItemVO.setXfqk(yxfs == xfzs ? "1" : "-1");
            jxsjxfztItemVO.setLastDownloadTime(DateUtil.format(jxDistributeStatus.getTDownloadTime(), DateUtil.DEFAULT_DATE_TIME));
            jxsjxfztItemVOS.add(jxsjxfztItemVO);
        }

        JxsjxfztlbVO result = new JxsjxfztlbVO();
        result.setTotalRows((int) page.getTotal());
        result.setJxsjxfztList(jxsjxfztItemVOS);
        return result;
    }

    @Override
    public JxsjxfztxqVO jxsjxfztxq(JxsjxfztxqDTO dto) {
        JxDistributeStatus jxDistributeStatus = modelMapper.selectByPrimaryKey(dto.getId());
        JxsjxfztxqVO result = new JxsjxfztxqVO();
        result.setJxPkgVersion(String.valueOf(jxDistributeStatus.getJxinfVersion()));
        result.setKcPkgStatus(String.valueOf(jxDistributeStatus.getJxinfPkgStatus()));
        return result;
    }

    @Override
    public JxsjxftjlbVO jxsjxftjlb(JxsjxftjlbDTO dto) {
        Example example = new Example(JxDistributeStatus.class);
        example.createCriteria().andEqualTo("jxTaskId", dto.getJxrwbh());
        List<JxDistributeStatus> jxDistributeStatuses = modelMapper.selectByExample(example);
        List<JxDistributeStatus> doneList = jxDistributeStatuses.stream().filter(item -> item.getJxinfPkgStatus() == DataDistributeStatusEnum.Done.getCode()).collect(Collectors.toList());
        int csjhxfsl = jxDistributeStatuses.size();
        int csywcxfsl = doneList.size();
        double wcl = 0;
        if (csjhxfsl != 0) {
            wcl = (csywcxfsl * 100.0) / csjhxfsl;
        }

        JxsjxftjlbVO result = new JxsjxftjlbVO();
        result.setCsjhxfsl(csjhxfsl);
        result.setCsywcxfsl(csywcxfsl);
        result.setCswwcxfsl(csjhxfsl - csywcxfsl);
        result.setWcl(String.format("%.2f", wcl));
        return result;
    }

    @Override
    public JxsjxftjWwcVO jxsjxftjWwc(JxsjxftjWwcDTO dto) {
        Example example = new Example(JxDistributeStatus.class);
        example.createCriteria().andEqualTo("jxTaskId", dto.getJxrwbh());
        example.and().andNotEqualTo("jxinfPkgStatus", DataDistributeStatusEnum.Done.getCode())
                .orIsNull("jxinfPkgStatus");
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<JxDistributeStatus> jxDistributeStatuses = modelMapper.selectByExample(example);

        List<String> jshList = jxDistributeStatuses.stream().map(JxDistributeStatus::getJsh).collect(Collectors.toList());
        List<JxJsxxDo> jxJsxxDos = csJsjbxxMapper.selectJsxxList(jshList, null);
        Map<String, String> csMap = jxJsxxDos.stream().collect(Collectors.toMap(JxJsxxDo::getJsh, JxJsxxDo::getJsmc, (oldV, newV) -> newV));
        List<JxsbxxDo> jxsbxxlb = sbcsgxMapper.jxsbxxlb(jshList, null, null);
        Map<String, JxsbxxDo> sbMap = jxsbxxlb.stream().collect(Collectors.toMap(JxsbxxDo::getJsh, Function.identity(), (oldV, newV) -> newV));

        List<JxsjxfsjWwcItemVO> jxsjxfsjWwcList = new ArrayList<>();
        for (JxDistributeStatus jxDistributeStatus : jxDistributeStatuses) {
            String jsh = jxDistributeStatus.getJsh();
            JxsjxfsjWwcItemVO jxsjxfsjWwcItemVO = new JxsjxfsjWwcItemVO();
            jxsjxfsjWwcItemVO.setCsmc(csMap.getOrDefault(jsh, "-"));

            JxsbxxDo jxsbxxDo = sbMap.get(jsh);
            // 设置设备信息
            if (jxsbxxDo != null) {
                if (jxDistributeStatus.getJxinfPkgStatus() == DataDistributeStatusEnum.Wait.getCode()) {
                    jxsjxfsjWwcItemVO.setZt("-2");
                } else {
                    jxsjxfsjWwcItemVO.setZt(jxsbxxDo.getZxzt());
                }
                jxsjxfsjWwcItemVO.setIpdz(jxsbxxDo.getIpdz());
            } else {
                jxsjxfsjWwcItemVO.setZt("-1");
                jxsjxfsjWwcItemVO.setIpdz("-");
            }
            jxsjxfsjWwcList.add(jxsjxfsjWwcItemVO);
        }
        JxsjxftjWwcVO result = new JxsjxftjWwcVO();
        result.setTotalRows((int) page.getTotal());
        result.setJxsjxfsjWwcList(jxsjxfsjWwcList);
        return result;
    }
}