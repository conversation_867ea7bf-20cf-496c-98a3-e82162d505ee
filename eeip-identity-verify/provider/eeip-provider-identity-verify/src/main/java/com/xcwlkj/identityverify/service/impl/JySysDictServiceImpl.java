/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.dfs.model.vo.UploadItemVO;
import com.xcwlkj.identityverify.mapper.JySysDictMapper;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.dto.sysconfig.*;
import com.xcwlkj.identityverify.model.enums.MqttConfigEnum;
import com.xcwlkj.identityverify.model.vo.sbzc.DzghVO;
import com.xcwlkj.identityverify.model.vo.sysconfig.*;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.HbTHParamEnum;
import com.xcwlkj.identityverify.third.superior.http.model.enums.HisomeParamEnum;
import com.xcwlkj.identityverify.third.superior.http.model.enums.JsJFParamEnum;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统字典表服务
 * <AUTHOR>
 * @version $Id: JySysDictServiceImpl.java, v 0.1 2023年09月19日 17时14分 xcwlkj.com Exp $
 */
@Service("jySysDictService")
@Slf4j
public class JySysDictServiceImpl extends BaseServiceImpl<JySysDictMapper, JySysDict> implements JySysDictService {

    @Resource
    private JySysDictMapper modelMapper;

    @Resource
    private ProvincePlatformClient provincePlatformClient;
    @Resource
    private RedisUtil redisUtil;
    private static final String SYNC_DEVICE_TIME_KEY = "syncDeviceTimeKey";



    @Override
    public JySysDict queryConfigValueByKey(String key) {
        Example example = new Example(JySysDict.class);
        example.createCriteria().andEqualTo("tCode", key);
        return modelMapper.selectOneByExample(example);
    }

    @Override
    public void sjptxxpz(SjptxxpzDTO dto) {
        String url = dto.getUrl();
        if(StringUtils.isNotBlank(url)){
            this.updateTValueByTCode(GlobalKeys.PROVINCE_URL_KEY,url);
            redisUtil.set(GlobalKeys.PROVINCE_URL_KEY,url,600);
        }
        String username = dto.getUsername();
        if (StringUtils.isNotBlank(username)){
            this.updateTValueByTCode(GlobalKeys.PROVINCE_USERNAME_KEY,username);
            redisUtil.set(GlobalKeys.PROVINCE_USERNAME_KEY,username,600);
        }
        String password = dto.getPassword();
        if (StringUtils.isNotBlank(password)){
            this.updateTValueByTCode(GlobalKeys.PROVINCE_PASSWORD_KEY,password);
            redisUtil.set(GlobalKeys.PROVINCE_PASSWORD_KEY,password,600);
        }
    }

    @Override
    public void updateTValueByTCode(String tCode, String tValue) {
        modelMapper.updateTValueByTCode(tCode,tValue);
    }

    @Override
    public void createOrUpdateSysDict(String tCode, String tName, String tValue, String tType, String tCatalog) {
        // 先查询是否存在
        JySysDict existingDict = queryConfigValueByKey(tCode);
        
        if (existingDict != null) {
            // 记录存在，更新值
            updateTValueByTCode(tCode, tValue);
        } else {
            // 记录不存在，创建新记录
            JySysDict newDict = new JySysDict();
            newDict.setId(IdGenerateUtil.generateId());
            newDict.setTCode(tCode);
            newDict.setTName(tName);
            newDict.setTValue(tValue != null ? tValue : "");
            newDict.setTType(tType);
            newDict.setTCatalog(tCatalog);
            newDict.setCreateTime(new Date());
            newDict.setUpdateTime(new Date());
            modelMapper.insertSelective(newDict);
        }
    }

    @Override
    public HqsjptpzxxVO hqsjptpzxx() {
        HqsjptpzxxVO hqsjptpzxxVO = new HqsjptpzxxVO();
        hqsjptpzxxVO.setUrl(this.queryConfigValueByKey(GlobalKeys.PROVINCE_URL_KEY).getTValue());
        hqsjptpzxxVO.setUsername(this.queryConfigValueByKey(GlobalKeys.PROVINCE_USERNAME_KEY).getTValue());
        return hqsjptpzxxVO;
    }

    @Override
    public void sbsbxtpz(SbsbxtpzDTO dto) {
        List<String> types = dto.getTypes();
        // 检查类型是否合法
//        if (!CollectionUtils.isEmpty(types)) {
//            for (String type : types) {
//                if (!StringUtils.isNotBlank(JcsblxEnum.getCodeByMsg(type))) {
//                    throw new RuntimeException("设备类型不合法");
//                }
//            }
//        }
        String tValue = StringUtils.join(types, ",");
        JySysDict jySysDict = this.queryConfigValueByKey(GlobalKeys.PROVINCE_SUBDEV_TYPE);
        // 如果存在则更新，不存在则插入
        if (jySysDict != null) {
            this.updateTValueByTCode(GlobalKeys.PROVINCE_SUBDEV_TYPE, tValue);
        } else {
            JySysDict sysDict = new JySysDict();
            // id为最大id+1
            Example example = new Example(JySysDict.class);
            example.setOrderByClause("id desc");
            List<JySysDict> jySysDicts = modelMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(jySysDicts)) {
                String preId = jySysDicts.get(0).getId();
                int nextId = Integer.parseInt(preId) + 1;
                String formattedId = String.format("%03d", nextId); // "%03d" 表示至少三位数字，不足时前面补零
                sysDict.setId(formattedId);
            } else {
                sysDict.setId("001");
            }
            sysDict.setTCode(GlobalKeys.PROVINCE_SUBDEV_TYPE);
            sysDict.setTValue(tValue);
            sysDict.setTName("省平台上报设备类型");
            sysDict.setTCatalog("hisomeProvicePlat");
            sysDict.setTType("upperPlatDock");
            modelMapper.insertSelective(sysDict);
        }
    }

    @Override
    public HqsbsbpzxxVO hqsbsbpzxx() {
        HqsbsbpzxxVO hqsbsbpzxxVO = new HqsbsbpzxxVO();
        String tValue = this.queryConfigValueByKey(GlobalKeys.PROVINCE_SUBDEV_TYPE).getTValue();
        String[] types = tValue.split(",");
        hqsbsbpzxxVO.setTypes(Arrays.asList(types));
        return hqsbsbpzxxVO;
    }

    @Override
    public PtpzcscxVO ptpzcscx(PtpzcscxDTO dto) {
        PtpzcscxVO ptpzcscxVO = new PtpzcscxVO();
        String type = dto.getType();
        List<PlatParamsItemVO> platParams = new ArrayList<>();
        SuperiorPlatEnum superiorPlatEnum = getSuperiorPlatEnum(type);
        switch (superiorPlatEnum) {
            case HB_TH:
                platParams = this.getPlatParamsByType(HbTHParamEnum.class);
                break;
            case JS_JF:
                platParams = this.getPlatParamsByType(JsJFParamEnum.class);
                break;

            case HISOME:
                platParams = this.getPlatParamsByType(HisomeParamEnum.class);
                break;
            default:
                break;
        }
        ptpzcscxVO.setPlatParams(platParams);
        return ptpzcscxVO;
    }

    private static SuperiorPlatEnum getSuperiorPlatEnum(String type) {
        SuperiorPlatEnum superiorPlatEnum = null;
        for (SuperiorPlatEnum platTypeEnum : SuperiorPlatEnum.values()) {
            if (platTypeEnum.getCode().equals(type)) {
                superiorPlatEnum = platTypeEnum;
            }
        }
        if (superiorPlatEnum == null) {
            throw new RuntimeException("平台类型不合法");
        }
        return superiorPlatEnum;
    }

    @Override
    public void ptpzxxsz(PtpzxxszDTO dto) {
        String platCode = dto.getPlatCode();
        List<ParamsItemDTO> params = dto.getParams();
        for (ParamsItemDTO param : params) {
            if(!StringUtils.isBlank(param.getCode())){
                String paramName = param.getCode();
                String paramValue = param.getValue();
                String tCode = platCode + "_" + paramName;
                this.updateTValueByTCode(tCode, paramValue);
            }
        }
        if(StringUtil.equals(dto.getPlatCode(),SuperiorPlatEnum.HISOME.getCode())){
            provincePlatformClient.clearUserInfoCache();
        }
    }

    @Override
    public void removeByTCode(List<String> tCodes) {
        Example example = new Example(JySysDict.class);
        example.createCriteria().andIn("tCode",tCodes);
        modelMapper.deleteByExample(example);
    }

    @Override
    public void mrsjptsz(MrsjptszDTO dto) {
        JySysDict defaultPlat = this.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
        getSuperiorPlatEnum(dto.getMrpt());
        if(defaultPlat != null) {
            this.updateTValueByTCode(GlobalKeys.DEFAULT_PLAT, dto.getMrpt());
            redisUtil.set(GlobalKeys.PROVINCE_PLAT_INFO,null);
        }else{
            JySysDict sysDict = new JySysDict();
            sysDict.setId(IdGenerateUtil.generateId());
            sysDict.setTCode(GlobalKeys.DEFAULT_PLAT);
            sysDict.setTValue(dto.getMrpt());
            sysDict.setTName("默认上报平台");
            sysDict.setTCatalog("hisomeProvicePlat");
            sysDict.setTType("upperPlatDock");
            sysDict.setCreateTime(new Date());
            sysDict.setUpdateTime(new Date());
            modelMapper.insertSelective(sysDict);
        }
    }

    private List<PlatParamsItemVO> getPlatParamsByType(Class<?> paramEnumClz) {
        List<PlatParamsItemVO> platParamsItemVOS = new ArrayList<>();
        Object[] enumConstants = paramEnumClz.getEnumConstants();
        try {
            // 获取每个枚举类的 code 和 name 字段
            for (Object enumConstant : enumConstants) {
                Field codeField = paramEnumClz.getDeclaredField("code");
                Field valueField = paramEnumClz.getDeclaredField("name");
                codeField.setAccessible(true);
                valueField.setAccessible(true);
                String code = (String) codeField.get(enumConstant);
                String value = (String) valueField.get(enumConstant);
                PlatParamsItemVO platParamsItemVO = new PlatParamsItemVO();
                platParamsItemVO.setParamName(code);
                platParamsItemVO.setParamNameDesc(value);
                platParamsItemVOS.add(platParamsItemVO);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return platParamsItemVOS;
    }

    @Override
    public WjfwcspzcxVO wjfwcspzcx(WjfwcspzcxDTO dto) {
        Example example = new Example(JySysDict.class);
        example.createCriteria().andEqualTo("tType", "wjfwdz");
        List<JySysDict> jySysDicts = modelMapper.selectByExample(example);
        List<WjfwcspzcxItemVO> wjfwcspzList = new ArrayList<>();
        for (JySysDict jySysDict : jySysDicts) {
            WjfwcspzcxItemVO wjfwcspzcxItemVO = new WjfwcspzcxItemVO();
            wjfwcspzcxItemVO.setCode(jySysDict.getTCode());
            wjfwcspzcxItemVO.setName(jySysDict.getTName());
            wjfwcspzcxItemVO.setValue(jySysDict.getTValue());
            wjfwcspzList.add(wjfwcspzcxItemVO);
        }
        WjfwcspzcxVO wjfwcspzcxVO = new WjfwcspzcxVO();
        wjfwcspzcxVO.setWjfwcspzcxList(wjfwcspzList);
        return wjfwcspzcxVO;
    }

    @Override
    public void wjfwcspzbc(WjfwcspzbcDTO dto) {
        for (WjfwcspzbcItemDTO wjfwcspzItemDTO : dto.getWjfwcspzbcList()) {
            String code = wjfwcspzItemDTO.getCode();
            String value = wjfwcspzItemDTO.getValue();
            JySysDict jySysDict = new JySysDict();
            jySysDict.setTValue(value);

            Example example = new Example(JySysDict.class);
            example.createCriteria().andEqualTo("tType", "wjfwdz").andEqualTo("tCode", code);
            modelMapper.updateByExampleSelective(jySysDict, example);
        }
    }

    @Override
    public MqttpzcxVO mqttpzcx(MqttpzcxDTO dto) {
        Example example = new Example(JySysDict.class);
        example.createCriteria().andEqualTo("tType", MqttConfigEnum.MQTT_IP.getType());
        List<JySysDict> jySysDicts = modelMapper.selectByExample(example);
        List<MqttpzcxItemVO> mqttpzcxList = new ArrayList<>();
        for (JySysDict jySysDict : jySysDicts) {
            MqttpzcxItemVO mqttpzcxItemVO = new MqttpzcxItemVO();
            mqttpzcxItemVO.setCode(jySysDict.getTCode());
            mqttpzcxItemVO.setName(jySysDict.getTName());
            mqttpzcxItemVO.setValue(jySysDict.getTValue());
            mqttpzcxList.add(mqttpzcxItemVO);
        }
        MqttpzcxVO mqttpzcxVO = new MqttpzcxVO();
        mqttpzcxVO.setMqttpzcxList(mqttpzcxList);
        return mqttpzcxVO;
    }

    @Override
    public void mqttpzbc(MqttpzbcDTO dto) {
        for (MqttpzbcItemDTO mqttpzbcItemDTO : dto.getMqttpzbcList()) {
            String code = mqttpzbcItemDTO.getCode();
            String value = mqttpzbcItemDTO.getValue();
            JySysDict jySysDict = new JySysDict();
            jySysDict.setTValue(value);

            Example example = new Example(JySysDict.class);
            example.createCriteria().andEqualTo("tType", MqttConfigEnum.get(code).getType()).andEqualTo("tCode", code);
            modelMapper.updateByExampleSelective(jySysDict, example);
        }
    }

    @Override
    public DzghVO getDzgh() {
        DzghVO dzghVO = new DzghVO();
        MqttConfigEnum[] configEnums = MqttConfigEnum.values();
        List<String> codeList = Arrays.stream(configEnums).map(MqttConfigEnum::getCode).distinct().collect(Collectors.toList());
        Example example = new Example(JySysDict.class);
        example.createCriteria().andEqualTo("tType", configEnums[0].getType()).andIn("tCode", codeList);
        List<JySysDict> dictList = modelMapper.selectByExample(example);
        String mqttIp = null;
        String mqttPort = null;
        String mqttUsername = null;
        String mqttPassword = null;
        for (JySysDict dict : dictList) {
            if (StringUtils.equals(MqttConfigEnum.MQTT_IP.getCode(), dict.getTCode())) {
                mqttIp = dict.getTValue();
            }
            if (StringUtils.equals(MqttConfigEnum.MQTT_PORT.getCode(), dict.getTCode())) {
                mqttPort = dict.getTValue();
            }
            if (StringUtils.equals(MqttConfigEnum.MQTT_USERNAME.getCode(), dict.getTCode())) {
                mqttUsername = dict.getTValue();
            }
            if (StringUtils.equals(MqttConfigEnum.MQTT_PASSWORD.getCode(), dict.getTCode())) {
                mqttPassword = dict.getTValue();
            }
        }
        dzghVO.setMqttip(mqttIp);
        dzghVO.setMqttport(mqttPort);
        dzghVO.setMqttUsername(mqttUsername);
        dzghVO.setMqttPassword(mqttPassword);
        dzghVO.setHttpip(mqttIp);
        dzghVO.setHttpport(mqttPort);
        dzghVO.setSchgatewayip(mqttIp);
        dzghVO.setSchgatewayport(mqttPort);
        List<DzghVO.Schgateway> schgatewayList = new ArrayList<>(1);
        DzghVO.Schgateway schgateway = new DzghVO.Schgateway();
        schgateway.setSchgatewayip(mqttIp);
        schgateway.setSchgatewayport(mqttPort);
        schgatewayList.add(schgateway);
        dzghVO.setSchgatewayList(schgatewayList);
        return dzghVO;
    }

    @Override
    public MqttpzljcsVO mqttpzljcs(MqttpzljcsDTO dto) {
        MqttpzljcsVO mqttpzljcsVO = new MqttpzljcsVO();

        String ip=dto.getMqttIp();
        String port=dto.getMqttPort();
        String username=dto.getMqttUsername();
        String password=dto.getMqttPassword();

        String brokerUrl = "tcp://" + ip + ":" + port;
        String clientId = "TestClient_" + System.currentTimeMillis();
        MqttAsyncClient client = null;
        try {
            // 创建 MQTT 客户端
            client = new MqttAsyncClient(brokerUrl, clientId);

            // 配置连接参数
            MqttConnectOptions options = new MqttConnectOptions();
            if (username != null) {
                options.setUserName(username);
            }
            if (password != null) {
                options.setPassword(password.toCharArray());
            }
            options.setCleanSession(true);
            options.setConnectionTimeout(3); // 连接超时时间（秒）
            options.setAutomaticReconnect(false); // 仅测试一次，不自动重连

            // 连接 MQTT 服务器
            log.info("正在连接到 MQTT 服务器: {}", brokerUrl);
            IMqttToken token = client.connect(options);
            token.waitForCompletion(); // 等待连接完成

            if (client.isConnected()) {
                log.info("MQTT 连接成功");
                mqttpzljcsVO.setStatus("1");
                mqttpzljcsVO.setMessage("连接成功！");
            } else {
                log.warn("MQTT 连接失败");
                mqttpzljcsVO.setStatus("0");
                mqttpzljcsVO.setMessage("连接失败！");
            }
        } catch (MqttException e) {
            log.error("MQTT 连接异常！错误码: {}，错误信息: {}", e.getReasonCode(), e.getMessage(), e);
            mqttpzljcsVO.setStatus("0");
            mqttpzljcsVO.setMessage("MQTT 连接异常！错误信息:" + e.getMessage());
        } finally {
            // 断开连接
            if (client != null) {
                try {
                    if (client.isConnected()) {
                        client.disconnect();
                        log.info("MQTT 连接已断开");
                    }
                } catch (MqttException e) {
                    log.warn("断开 MQTT 连接失败: {}", e.getMessage(), e);
                }
            }
        }
        return mqttpzljcsVO;
    }

    @Override
    public void jrtbsbsjdql() {
        redisUtil.del(SYNC_DEVICE_TIME_KEY);
    }

    @Override
    public SjwjfwsccsVO sjwjfwsccs(SjwjfwsccsDTO dto) {
        SjwjfwsccsVO result = new SjwjfwsccsVO();
        result.setStatus("0");
        result.setMessage("上级文件服务上传测试失败");
        
        try {
            // 从jy_sys_dict表中读取恒生平台的文件服务器配置
            JySysDict fileServerUrl = queryConfigValueByKey("HISOME_fileServerUrl");
            JySysDict fileServerChannel = queryConfigValueByKey("HISOME_fileServerChannel");
            JySysDict fileServiceAppId = queryConfigValueByKey("HISOME_fileServiceAppId");
            JySysDict fileServiceAppSecret = queryConfigValueByKey("HISOME_fileServiceAppSecret");
            
            if (fileServerUrl == null || StringUtil.isBlank(fileServerUrl.getTValue())) {
                result.setMessage("文件服务器地址未配置");
                return result;
            }
            
            if (fileServerChannel == null || StringUtil.isBlank(fileServerChannel.getTValue())) {
                result.setMessage("文件服务渠道未配置");
                return result;
            }
            
            if (fileServiceAppId == null || StringUtil.isBlank(fileServiceAppId.getTValue())) {
                result.setMessage("文件服务appId未配置");
                return result;
            }
            
            if (fileServiceAppSecret == null || StringUtil.isBlank(fileServiceAppSecret.getTValue())) {
                result.setMessage("文件服务appSecret未配置");
                return result;
            }
            
            // 检查和获取token
            String token = getOrRefreshUpperHsToken(
                fileServerUrl.getTValue(),
                fileServerChannel.getTValue(),
                fileServiceAppId.getTValue(),
                fileServiceAppSecret.getTValue()
            );
            
            if (StringUtil.isBlank(token)) {
                result.setMessage("获取上级文件服务token失败");
                return result;
            }
            
            // 执行上传测试
            String imageUploadResult = testImageUpload();
            String textUploadResult = testTextUpload();
            
            result.setImageUploadDetail(imageUploadResult);
            result.setTextUploadDetail(textUploadResult);
            result.setStatus("1");
            result.setMessage("上级文件服务上传测试成功");
            
        } catch (Exception e) {
            log.error("上级文件服务上传测试异常", e);
            result.setMessage("上传测试异常：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取或刷新上级恒生文件服务token
     * 先检查redis缓存，如果不存在或过期则重新获取
     */
    private String getOrRefreshUpperHsToken(String serverUrl, String channel, String appId, String appSecret) {
        String tokenKey = "UPPER_HS_FILE_TOKEN";
        
        // 先从redis中获取token
        String cachedToken = (String) redisUtil.get(tokenKey);
        if (StringUtil.isNotBlank(cachedToken)) {
            log.info("从缓存中获取到上级文件服务token");
            return cachedToken;
        }
        
        try {
            // 初始化UpperHsDfsClient
            com.xcwlkj.identityverify.util.UpperHsDfsClient.init(serverUrl, channel, appId, appSecret);
            
            // 获取新的token
            String newToken = com.xcwlkj.identityverify.util.UpperHsDfsClient.getToken();
            
            if (StringUtil.isNotBlank(newToken)) {
                // 缓存token，默认30天过期
                redisUtil.set(tokenKey, newToken, 30 * 24 * 60 * 60);
                log.info("成功获取并缓存上级文件服务token");
                return newToken;
            }
        } catch (Exception e) {
            log.error("获取上级文件服务token失败", e);
        }
        
        return null;
    }
    
    /**
     * 测试图片上传
     */
    private String testImageUpload() {
        String testImagePath = "/tmp/identityverify/test_image.jpg";
        File imageFile = new File(testImagePath);
        
        try {
            // 创建测试图片文件
            if (!imageFile.exists()) {
                imageFile.getParentFile().mkdirs();
                // 创建一个简单的测试图片内容（这里只是模拟）
                try (FileWriter writer = new FileWriter(imageFile)) {
                    writer.write("TEST_IMAGE_CONTENT_FOR_UPLOAD_TEST");
                }
            }
            
            // 使用UpperHsDfsClient上传图片
            com.xcwlkj.dfs.model.vo.UploadVO uploadResult = com.xcwlkj.identityverify.util.UpperHsDfsClient.uploadStream(testImagePath);
            UploadItemVO uploadItemVO = uploadResult.getList().get(0);
            if (uploadItemVO != null) {
                return "图片上传成功，文件ID：" + uploadItemVO.getId() + "，文件路径：" + uploadItemVO.getFilePath();
            } else {
                return "图片上传失败：返回结果为空";
            }
            
        } catch (Exception e) {
            log.error("图片上传测试失败", e);
            return "图片上传失败：" + e.getMessage();
        } finally {
            // 清理测试文件
            if (imageFile.exists()) {
                imageFile.delete();
            }
        }
    }
    
    /**
     * 测试文本文件上传
     */
    private String testTextUpload() {
        String testTextPath = "/tmp/identityverify/test_text.txt";
        File textFile = new File(testTextPath);
        
        try {
            // 创建测试文本文件
            if (!textFile.exists()) {
                textFile.getParentFile().mkdirs();
                try (FileWriter writer = new FileWriter(textFile)) {
                    writer.write("这是上级文件服务上传测试的文本内容\n");
                    writer.write("测试时间：" + new Date() + "\n");
                    writer.write("测试目的：验证上级文件服务连接和上传功能");
                }
            }
            
            // 使用UpperHsDfsClient上传文本文件
            com.xcwlkj.dfs.model.vo.UploadVO uploadResult = com.xcwlkj.identityverify.util.UpperHsDfsClient.uploadStream(testTextPath);
            
            if (uploadResult != null) {
                UploadItemVO uploadItemVO = uploadResult.getList().get(0);
                return "文本上传成功，文件ID：" + uploadItemVO.getId() + "，文件路径：" + uploadItemVO.getFilePath();
            } else {
                return "文本上传失败：返回结果为空";
            }
            
        } catch (Exception e) {
            log.error("文本上传测试失败", e);
            return "文本上传失败：" + e.getMessage();
        } finally {
            // 清理测试文件
            if (textFile.exists()) {
                textFile.delete();
            }
        }
    }
}