package com.xcwlkj.identityverify.provincePlatform.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetValidateInfoReq extends BaseRequest implements Serializable {

    /** 加密的JSON字符串，使用固定秘钥hssfhy@2025#@! */
    private String encrptJson;

    /** 设备序列号 */
    private String sbxlh;
}
