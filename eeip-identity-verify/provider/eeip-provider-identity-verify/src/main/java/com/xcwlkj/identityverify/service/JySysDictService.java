/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.dto.sysconfig.*;
import com.xcwlkj.identityverify.model.vo.sysconfig.*;
import com.xcwlkj.identityverify.model.vo.sbzc.DzghVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 系统字典表服务
 * <AUTHOR>
 * @version $Id: JySysDictService.java, v 0.1 2023年09月19日 17时14分 xcwlkj.com Exp $
 */
@Service
public interface JySysDictService extends BaseService<JySysDict> {

    JySysDict queryConfigValueByKey(String key);

    /**
     * 上级平台信息配置
     * @param dto
     */
    void sjptxxpz(SjptxxpzDTO dto);

    /**
     * 根据tCode更新tValue
     * @param tCode
     * @param tValue
     */
    void updateTValueByTCode(String tCode, String tValue);

    /**
     * 创建或更新系统字典记录
     * 如果记录不存在则创建，存在则更新
     * @param tCode
     * @param tName
     * @param tValue
     * @param tType
     * @param tCatalog
     */
    void createOrUpdateSysDict(String tCode, String tName, String tValue, String tType, String tCatalog);

    /**
     * 获取上级平台配置信息
     * @return
     */
    HqsjptpzxxVO hqsjptpzxx();

    /**
     * 设备上报信息配置
     * @param dto
     */
    void sbsbxtpz(SbsbxtpzDTO dto);

    /**
     * 获取设备上报配置信息
     * @return
     */
    HqsbsbpzxxVO hqsbsbpzxx();

    /**
     * 根据类型查询平台配置参数
     * @param dto
     * @return
     */
    PtpzcscxVO ptpzcscx(PtpzcscxDTO dto);

    /**
     * 平台配置信息设置
     * @param dto
     */
    void ptpzxxsz(PtpzxxszDTO dto);

    /**
     * 平台配置参数删除
     * @param tCodes
     */
    void removeByTCode(List<String> tCodes);

    /**
     * 设置默认上级平台
     * @param dto
     */
    void mrsjptsz(MrsjptszDTO dto);

    /**
     * 文件服务参数配置查询
     * @param dto
     * @return
     */
    WjfwcspzcxVO wjfwcspzcx(WjfwcspzcxDTO dto);

    /**
     * 文件服务参数配置保存
     * @param dto
     */
    void wjfwcspzbc(WjfwcspzbcDTO dto);

    /**
     * mqtt配置查询
     * @param dto
     * @return
     */
    MqttpzcxVO mqttpzcx(MqttpzcxDTO dto);

    /**
     * mqtt配置保存
     * @param dto
     */
    void mqttpzbc(MqttpzbcDTO dto);

    DzghVO getDzgh();

    /**
     * mqtt配置连接测试
     * @param dto
     * @return
     */
    MqttpzljcsVO mqttpzljcs(MqttpzljcsDTO dto);

    /**
     *  接入同步设备时间点清理
     */
    void jrtbsbsjdql();

    /**
     * 上级文件服务上传测试
     * @param dto
     * @return
     */
    SjwjfwsccsVO sjwjfwsccs(SjwjfwsccsDTO dto);
}