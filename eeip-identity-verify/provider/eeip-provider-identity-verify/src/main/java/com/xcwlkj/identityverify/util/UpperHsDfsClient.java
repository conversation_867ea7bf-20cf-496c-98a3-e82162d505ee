package com.xcwlkj.identityverify.util;

import com.alibaba.fastjson.JSON;
import com.xcwlkj.dfs.exceptions.DfsFileNotFoundException;
import com.xcwlkj.dfs.exceptions.DfsSdkBusiException;
import com.xcwlkj.dfs.model.*;
import com.xcwlkj.dfs.model.base.DfsBaseResp;
import com.xcwlkj.dfs.model.vo.*;
import org.apache.commons.lang3.StringUtils;

import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UpperHsDfsClient {

	public 	static String token;	//TOKEN
	private static String serverUrl;//服务URL
	private static String channel;	//渠道
	private static String appId;	//应用id	
	private static String appSecret;//应用密钥

	/**
	 * 初始化
	 * 
	 * @param serverUrl 分布式文件子系统服务地址
	 * @param channel   渠道
	 * @param appId     应用id
	 * @param appSecret 应用密钥
	 * @return
	 */
	public static void init(String serverUrl, String channel, String appId, String appSecret) {
		serverUrl = serverUrl.replace("\\", "/");
		if (serverUrl.endsWith("/")) {
			serverUrl = serverUrl.substring(0, serverUrl.length() - 1);
		}
		UpperHsDfsClient.serverUrl = serverUrl;
		UpperHsDfsClient.channel = channel;
		UpperHsDfsClient.appId = appId;
		UpperHsDfsClient.appSecret = appSecret;
	}

	/**
	 * 获取token
	 * 
	 * @return
	 */
	public static String getToken() {
		String param = "{\"channel\": \"" + channel + "\",\"appId\": \"" + appId + "\",\"appSecret\": \"" + appSecret
				+ "\"}";
		String result = sendHttpReq(serverUrl + "/common/getConnectToken", param,null);
		TokenRespModel ret = JSON.parseObject(result, TokenRespModel.class);
		if(ret.getCode() != 200) {
		    throw new RuntimeException(ret.getMessage());
		}
		token = ret.getResult().getConnectToken();
		return token;
	}

	/**
	 * 文件流式上传
	 * 
	 * @param fileShortName 文件简名
	 * @param inputStream   输入流
	 * @return
	 */
	public static UploadVO uploadStream(String fileShortName, InputStream inputStream) {
		return uploadStream(fileShortName,inputStream,0);
	}
	
	public static UploadVO uploadStream(String fileShortName, InputStream inputStream,int operate) {
		return uploadStream(fileShortName,inputStream,operate,null);
	}
	/**
	 * 文件流式上传
	 * 
	 * @param fileName 文件名
	 * @return
	 */
	public static UploadVO uploadStream(String fileName) {
		return uploadStream(fileName,0);
	}
	public static UploadVO uploadStream(String fileName,int operate) {
		return uploadStream(fileName,operate,null);
	}
	/**
	 * 文件流式上传
	 * 
	 * @param fileName 文件名
	 * @param operate   0-无操作  1-缩略图
	 * @return
	 */
	public static UploadVO uploadStream(String fileName,int operate,String bucketName) {
		if (StringUtils.isBlank(fileName)) {
			throw new DfsSdkBusiException("fileName不能为空");
		}
		File file = new File(fileName);
		String fileShortName = file.getName();
		InputStream stream = null;
		try {
			stream = new FileInputStream(fileName);
		} catch (FileNotFoundException e) {
			throw new DfsSdkBusiException("获取文件流错误：fileName:" + fileName);
		}
		return uploadStream(fileShortName, stream,operate,bucketName);
	}
	
	/**
	 * 文件流式上传
	 * 
	 * @param fileShortName 文件简名
	 * @param inputStream   输入流
	 * @param operate   0-无操作  1-缩略图
	 * @return
	 */
	public static UploadVO uploadStream(String fileShortName, InputStream inputStream,int operate,String bucketName) {
		return uploadStream(fileShortName,inputStream,operate,bucketName,null);
	}

	/**
	 * 文件流式上传
	 *
	 * @param fileShortName 文件简名
	 * @param inputStream   输入流
	 * @param operate   0-无操作  1-缩略图
	 * @return
	 */
	public static UploadVO uploadStream(String fileShortName, InputStream inputStream,int operate,String bucketName,Integer expireHour) {
		DfsUploadReqModel reqModel = new DfsUploadReqModel();
		reqModel.setChannel(channel);
		if (StringUtils.isBlank(fileShortName)) {
			throw new DfsSdkBusiException("fileName不能为空");
		}
		reqModel.setFileName(fileShortName);
		reqModel.setOperate(operate);
		if(StringUtils.isBlank(bucketName)) {
			bucketName = "default";
		}
		reqModel.setBucketName(bucketName);
		if(expireHour!=null&&expireHour==0){
			throw new DfsSdkBusiException("expireHour不能为0");
		}
		String headReq = JSON.toJSONString(reqModel);
		try {
			headReq = URLEncoder.encode(headReq,"utf-8");
		} catch (UnsupportedEncodingException e1) {
			throw new DfsSdkBusiException("打包文件名encode错误！", e1);
		}

		OutputStream out = null;
		BufferedReader in = null;
		String result = "";
		try {
			String url = serverUrl + "/remote/uploadStream";
			URL realUrl = new URL(url);
			HttpURLConnection  conn = (HttpURLConnection )realUrl.openConnection();
			unCheckHttps(url,conn);
//			conn.setRequestProperty("Accept-Charset", "utf-8");
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("Content-Type", "application/octet-stream");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setRequestProperty("xc_dfs_req", headReq);
			conn.setRequestProperty("xc_client_sessionid", token);
			conn.setConnectTimeout(30 * 60 * 1000);
			conn.setReadTimeout(30 * 60 * 1000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestMethod("POST");
			out = conn.getOutputStream();
			byte buff[] = new byte[1024];
			int length = 0;
			while ((length = inputStream.read(buff)) > 0) {
				out.write(buff, 0, length);
			}
			out.flush();
			inputStream.close();
			in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			throw new DfsSdkBusiException("文件流式上传请求出现异常！", e);
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		if (!StringUtils.isBlank(result)) {
			UploadRespModel resp = JSON.parseObject(result, UploadRespModel.class);
			if (resp != null) {
				if(resp.getCode()==200) {
					return resp.getResult();
				}
				throw new DfsSdkBusiException(resp.getCode(),resp.getMessage());
			}
		}
		throw new DfsSdkBusiException("文件流式上传请求出现异常");
	}

	/**
	 * 通过文件路径下载文件
	 * 
	 * @param filePath 文件相对路径
	 * @return
	 */
	public static DownloadVO download(String filePath) {

		if (StringUtils.isBlank(filePath)) {
			throw new DfsSdkBusiException("filePath不能为空");
		}
		if (filePath.startsWith("http")) {
			throw new DfsSdkBusiException("filePath为相对路径，不包括Http部分");
		}
		if (filePath.startsWith("/")) {
			filePath = filePath.substring(1, filePath.length());
		}
		OutputStream out = null;
		InputStream inputStream = null;
		try {
			String url =serverUrl + "/remote/file/" + filePath;
			URL realUrl = new URL(url);
			URLConnection conn = realUrl.openConnection();
			unCheckHttps(url,conn);
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setConnectTimeout(30 * 60 * 1000);
			conn.setReadTimeout(30 * 60 * 1000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			inputStream = conn.getInputStream();
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
			byte[] buff = new byte[1024];
			int rc = 0;
			while ((rc = inputStream.read(buff, 0, 1024)) > 0) {
				byteArrayOutputStream.write(buff, 0, rc);
			}

			DownloadVO result = new DownloadVO();
			Map<String, List<String>> map = conn.getHeaderFields();
			map.forEach((key, value) -> {
				if (!StringUtils.isBlank(key) && key.toLowerCase().equals("orifilename")) {
					String oriFileName = value.stream().collect(Collectors.joining(""));
					try {
						oriFileName = URLDecoder.decode(oriFileName,"utf-8");
					} catch (UnsupportedEncodingException e) {
						throw new DfsSdkBusiException("文件名转换错误！", e);
					}
					result.setOriFileName(oriFileName);
				}
			});
			if(StringUtils.isBlank(result.getOriFileName())) {
				return null;
			}
			result.setContent(byteArrayOutputStream.toByteArray());
			return result;
		}catch(FileNotFoundException e) {
			throw new DfsFileNotFoundException("文件未发现！", e);
		}catch (Exception e) {
			throw new DfsSdkBusiException("发送 POST请求出现异常！", e);
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException ex) {
				throw new DfsSdkBusiException("发送 POST请求出现异常！");
			}
		}
	}

	/**
	 * 通过文对象下载文件
	 * 
	 * @param id 文件对象编号
	 * @return
	 */
	public static DownloadVO downloadByObj(String id) {

		if (StringUtils.isBlank(id)) {
			throw new DfsSdkBusiException("文件Id不能为空");
		}
		OutputStream out = null;
		InputStream inputStream = null;
		try {
			String url = serverUrl + "/remote/dfs/" + id;
			URL realUrl = new URL(url);
			URLConnection conn = realUrl.openConnection();
			unCheckHttps(url,conn);
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setConnectTimeout(30 * 60 * 1000);
			conn.setReadTimeout(30 * 60 * 1000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			inputStream = conn.getInputStream();
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
			byte[] buff = new byte[1024];
			int rc = 0;
			while ((rc = inputStream.read(buff, 0, 1024)) > 0) {
				byteArrayOutputStream.write(buff, 0, rc);
			}
			DownloadVO result = new DownloadVO();
			Map<String, List<String>> map = conn.getHeaderFields();
			map.forEach((key, value) -> {
				if (!StringUtils.isBlank(key) && key.toLowerCase().equals("orifilename")) {
					String oriFileName = value.stream().collect(Collectors.joining(""));
					try {
						oriFileName = URLDecoder.decode(oriFileName,"utf-8");
					} catch (UnsupportedEncodingException e) {
						throw new DfsSdkBusiException("文件名转换错误！", e);
					}
					result.setOriFileName(oriFileName);
				}
			});
			if(StringUtils.isBlank(result.getOriFileName())) {
				return null;
			}
			result.setContent(byteArrayOutputStream.toByteArray());
			return result;
		}catch(FileNotFoundException e) {
			throw new DfsFileNotFoundException("文件未发现！", e);
		}catch (Exception e) {
			throw new DfsSdkBusiException("文件下载请求出现异常！", e);
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
	}

	/**
	 * 文件信息查询
	 * 
	 * @param fileId 文件id列表
	 * @return 返回文件信息
	 */
	public static FileInfoVO queryFileInfo(String fileId) {
		
		if (StringUtils.isBlank(fileId)) {
			throw new DfsSdkBusiException("文件Id不能为空");
		}
		
		String param = "{\"id\": \"" + fileId  + "\"}";
		String result = sendHttpReq(serverUrl + "/remote/dfs/xcdfs/file-info", param,token);
		
		if (!StringUtils.isBlank(result)) {
			FileInfoRespModel resp = JSON.parseObject(result, FileInfoRespModel.class);
			if (resp != null) {
				if(resp.getCode()==200) {
					return resp.getResult();
				}
				throw new DfsSdkBusiException(resp.getCode(),resp.getMessage());
			}
		}
		throw new DfsSdkBusiException("文件信息查询请求出现异常");
	}
	
	/**
	 * 文件删除
	 * @param keys 文件id列表
	 */
	public static void delete(String ... keys) {
		delete(0,keys);
	}
	
	/**
	 * 文件删除
	 * @param type 删除类型 0-文件id  1-文件路径
	 * @param keys 文件id列表或者文件路径列表
	 * @return 
	 */
	public static void delete(int type,String ... keys) {
		
		if(keys.length<=0) {
			throw new DfsSdkBusiException("删除文件不能为空！");
		}
		BatchReqModel reqModel = new BatchReqModel();
		reqModel.setChannel(channel);
		reqModel.setType(type);
		reqModel.setKeys(Arrays.asList(keys));
		String result = sendHttpReq(serverUrl + "/remote/dfs/xcdfs/file-delete", JSON.toJSONString(reqModel),token);
		if (!StringUtils.isBlank(result)) {
			DfsBaseResp resp = JSON.parseObject(result, DfsBaseResp.class);
			if (resp != null) {
				if(resp.getCode()==200) {
					return;
				}
				throw new DfsSdkBusiException(resp.getCode(),resp.getMessage());
			}
		}
		throw new DfsSdkBusiException("删除文件请求出现异常");
	}
	/**
	 * 文件路径时间戳请求
	 * type 0-文件id  1-文件路径
	 * 
	 */
	public static TimePathVO timePath(Long expiredMinute,int type,String ... files) {
		
		if(files.length<=0) {
			throw new DfsSdkBusiException("files不能为空！");
		}
		TimePathReqModel reqModel = new TimePathReqModel();
		reqModel.setChannel(channel);
		reqModel.setType(type);
		reqModel.setExpiredMinute(expiredMinute);
		reqModel.setFiles(Arrays.asList(files));
		String result = sendHttpReq(serverUrl + "/remote/dfs/xcdfs/time-path", JSON.toJSONString(reqModel),token);
		if (!StringUtils.isBlank(result)) {
			TimePathRespModel resp = JSON.parseObject(result, TimePathRespModel.class);
			if (resp != null) {
				if(resp.getCode()==200) {
					return resp.getResult();
				}
				throw new DfsSdkBusiException(resp.getCode(),resp.getMessage());
			}
		}
		throw new DfsSdkBusiException("文件时间戳请求出现异常");
	}	
	
	

	private static String sendHttpReq(String url, String param,String xtoken) {

		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			URLConnection  conn = realUrl.openConnection();
			unCheckHttps(url,conn);
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			if(!StringUtils.isBlank(xtoken)) {
				conn.setRequestProperty("xc_client_sessionid", xtoken);
			}
			conn.setConnectTimeout(30 * 60 * 1000);
			conn.setReadTimeout(30 * 60 * 1000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			out = new PrintWriter(conn.getOutputStream());
			out.print(param);
			out.flush();
			in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			throw new DfsSdkBusiException("发送 POST请求出现异常！", e);
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				throw new DfsSdkBusiException("发送 POST请求出现异常！", ex);
			}
		}
		return result;
	}
	
	/*************************开始HTTPS处理*************************************************/
	private static void unCheckHttps(String url,URLConnection  conn) {
		boolean useHttps = url.toLowerCase().startsWith("https");
	      if (useHttps) {
	        HttpsURLConnection https = (HttpsURLConnection) conn;
	        trustAllHosts(https);
	        https.setHostnameVerifier(DO_NOT_VERIFY);
	      }
	}
	  /**
	   * 信任所有
	   * @param connection
	   * @return
	   */
	  private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) {
	    SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
	    try {
	      SSLContext sc = SSLContext.getInstance("TLS");
	      sc.init(null, trustAllCerts, new java.security.SecureRandom());
	      SSLSocketFactory newFactory = sc.getSocketFactory();
	      connection.setSSLSocketFactory(newFactory);
	    } catch (Exception e) {
	      e.printStackTrace();
	    }
	    return oldFactory;
	  }
	  
	  /**
	   * 设置不验证主机
	   */
	  private static final HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
	    public boolean verify(String hostname, SSLSession session) {
	      return true;
	    }
	  };
	  
	  /**
	   * 覆盖java默认的证书验证
	   */
	  private static final TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
	    public X509Certificate[] getAcceptedIssuers() {
	      return new X509Certificate[]{};
	    }

	    public void checkClientTrusted(X509Certificate[] chain, String authType)
	            throws CertificateException {
	    }

	    public void checkServerTrusted(X509Certificate[] chain, String authType)
	            throws CertificateException {
	    }
	  }};
	
	  /*************************结束HTTPS处理*************************************************/	
}
