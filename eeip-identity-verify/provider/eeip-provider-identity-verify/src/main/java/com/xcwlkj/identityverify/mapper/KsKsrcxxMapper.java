/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.mapper;

import com.xcwlkj.identityverify.config.commonMapper.CustomMapper;
import com.xcwlkj.identityverify.model.dos.*;
import com.xcwlkj.identityverify.model.dto.cxtj.KsrcZeroLbDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KsrcqkxqcxDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.RcwbsjLbDTO;
import com.xcwlkj.identityverify.model.vo.cxtj.KsrcqkDatasItemVO;
import com.xcwlkj.identityverify.model.vo.cxtj.RcwbsjItemVO;
import com.xcwlkj.identityverify.model.vo.cxtj.ZeroKcItemVO;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.KshyqkVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 考生入场信息数据库操作
 * <AUTHOR>
 * @version $Id: InitKsKsrcxxMapper.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKsrcxxMapper extends CustomMapper<KsKsrcxx> {


    List<KsrcqkDatasItemVO> ksrcqkxqcx(@Param("dto") KsrcqkxqcxDTO ksrcqkxqcxDTO, @Param("bzhkcids") List<String> bzhkcids);

    /**
     * 查询考场入场人数
     *
     * @param ksjhbh   考试计划编号
     * @param ccm      考场码
     * @param kdbhList 考点编号集合
     * @param bzhkcids
     * @return
     */
    List<KsRcRsDO> cxkcrcrs(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm, @Param("kdbhList") List<String> kdbhList, List<String> bzhkcids);

    List<KcksqkDO> kcksqk(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm, @Param("ljkcbhList") List<String> ljkcbhList);

    List<KcksqkTjDO> kcksqkTj(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm);

    /**
     * 根据考试计划和考试场次查询考生入场统计信息
     * @param ksjhbh
     * @param ccm
     * @return
     */
    KshyqkVO kshyqkcx(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm);

    SjsbTjxxDO getSjsbTjxx(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm);

    void batchUpdateTbzt(@Param("ksrcxxList") List<KsKsrcxx> ksrcxxList);

    int initByKsjhbh(@Param("ksjhbh") String ksjhbh);

    List<ZeroKcItemVO> ksrcZeroLb(@Param("dto") KsrcZeroLbDTO dto);

    List<RcwbsjItemVO> rcwbsjLb(@Param("dto") RcwbsjLbDTO dto);

    List<KsLjkcRcRsDO> cxljkcrcrs(@NotBlank(message = "考试计划编号不能为空") String ksjhbh, @NotBlank(message = "场次码不能为空") String ccm, List<String> kdbhList, List<String> bzhkcids);

    /**
     * 根据考试计划编号和场次码查询考生入场信息用于上传到上级平台
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param offset 偏移量
     * @param limit 限制条数
     * @return 考生入场信息列表
     */
    List<KsKsrcxx> selectForUploadToProvince(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计符合条件的考生入场信息总数
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 总数
     */
    int countForUploadToProvince(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm);
}
