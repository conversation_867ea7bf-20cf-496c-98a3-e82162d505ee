package com.xcwlkj.identityverify.provincePlatform.request.items;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetValidateInfoItem implements Serializable {

    /** 验证时间yyyy-MM-dd HH:mm:ss */
    private String yzsj;

    /** 核验结果 1-通过 0-不通过 */
    private String hyjg;

    /** 准考证号 */
    private String zkzh;

    /** 考点代码 */
    private String kddm;

    /** 考场号 */
    private String kch;

    /** 刷证结果 1-通过 0-不通过 */
    private String szjg;

    /** 人脸识别结果 1-通过 0-不通过 */
    private String rlsbjg;

    /** 指纹认证结果 1-通过 0-不通过 */
    private String zwrzjg;

    /** 相似度 */
    private String xsd;

    /** 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场 */
    private String sjyxj;
}
