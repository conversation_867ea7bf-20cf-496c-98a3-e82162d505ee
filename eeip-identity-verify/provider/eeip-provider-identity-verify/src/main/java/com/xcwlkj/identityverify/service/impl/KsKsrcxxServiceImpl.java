/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.dfs.model.vo.TimePathVO;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.identityverify.mapper.KsKsrcxxMapper;
import com.xcwlkj.identityverify.model.domain.KsBmxx;
import com.xcwlkj.identityverify.model.domain.KsKcxx;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.domain.KsKssjDistributeStatus;
import com.xcwlkj.identityverify.model.dos.KsLjkcRcRsDO;
import com.xcwlkj.identityverify.model.dos.KsRcRsDO;
import com.xcwlkj.identityverify.model.dos.SjsbTjxxDO;
import com.xcwlkj.identityverify.model.dto.attachment.GetAttachUrlDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.*;
import com.xcwlkj.identityverify.model.enums.KsrcxxSbztEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.model.vo.attachment.AttachFileVoItemVO;
import com.xcwlkj.identityverify.model.vo.attachment.GetAttachUrlVO;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.cxtj.*;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.identityverify.template.KsrcqkxqTemplate;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.KshyqkVO;
import com.xcwlkj.identityverify.util.CompressUtil;
import com.xcwlkj.identityverify.util.HsUtils;
import com.xcwlkj.identityverify.util.UnifyAccessUtils;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.common.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * 考生入场信息服务
 *
 * <AUTHOR>
 * @version $Id: KsKsrcxxServiceImpl.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Service("ksKsrcxxService")
@Slf4j
public class KsKsrcxxServiceImpl extends BaseServiceImpl<KsKsrcxxMapper, KsKsrcxx> implements KsKsrcxxService {

    @Resource
    private KsKsrcxxMapper modelMapper;
    @Resource
    private KsKcxxService ksKcxxService;
    @Resource
    private KsBmxxService ksBmxxService;
    @Resource
    private CsXxjbxxService csXxjbxxService;
    @Resource
    private SbSbxxService sbSbxxService;
    @Resource
    private KsKssjDistributeStatusService ksKssjDistributeStatusService;
    @Resource
    private UnifyAccessUtils unifyAccessUtils;
    @Resource
    private AttachmentHandler attachmentHandler;
    @Value("${xc.xcDfs.filePathPrefix}")
    private String filePathPrefix;
    @Value("${xc.temp.generatePath}")
    private String generatePath;
    // 创建HTTP客户端（在类级别初始化一次）
    private static final CloseableHttpClient httpClient = HttpClients.custom()
            .setMaxConnTotal(50)
            .setMaxConnPerRoute(20)
            .setConnectionTimeToLive(10, TimeUnit.SECONDS)
            .build();
    @Override
    public void insertList(List<KsKsrcxx> ksrcxxList) {
        modelMapper.insertList(ksrcxxList);
    }

    @Override
    public KsrcqkxqcxVO ksrcqkxqcx(KsrcqkxqcxDTO ksrcqkxqcxDTO) {
        String ksjhbh = ksrcqkxqcxDTO.getKsjhbh();
        String csbh = ksrcqkxqcxDTO.getCsbh();
        String cslx = ksrcqkxqcxDTO.getCslx();
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(csbh, cslx);
//        List<KsKcxx> bzhkcListByKsjhbh = ksKcxxMapper.getBzhkcList(ksjhbh);
//        List<String> bzhkcidsByKsjhbh = bzhkcListByKsjhbh.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
//        bzhkcids.retainAll(bzhkcidsByKsjhbh);
        KsrcqkxqcxVO ksrcqkxqcxVO = new KsrcqkxqcxVO();
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderCol())) {
            ksrcqkxqcxDTO.setOrderCol("KCBH,ZWH_PX");
        }
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderType())) {
            ksrcqkxqcxDTO.setOrderType("ASC");
        }
        Page<Object> page = PageHelper.startPage(ksrcqkxqcxDTO.getPageNum(), ksrcqkxqcxDTO.getPageSize());
        List<KsrcqkDatasItemVO> ksrcqkxqcxVOList = modelMapper.ksrcqkxqcx(ksrcqkxqcxDTO, bzhkcids);
        ksrcqkxqcxVOList.forEach(k -> {
            String sfzh = k.getSfzh();
            // 身份证号脱敏
            k.setSfzh(HsUtils.EncryptionSFZH(sfzh));
        });
        ksrcqkxqcxVO.setDatas(ksrcqkxqcxVOList);
        ksrcqkxqcxVO.setTotalRows((int) page.getTotal());
        return ksrcqkxqcxVO;
    }

    @Override
    public KsrctjVO ksrctj(KsrctjDTO ksrctjDTO) {
        List<String> kdbhList = ksrctjDTO.getCsbh() == null ? null : Collections.singletonList(ksrctjDTO.getCsbh());
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(ksrctjDTO.getCsbh(), ksrctjDTO.getCslx());
        List<KsRcRsDO> ksrckcs = modelMapper.cxkcrcrs(ksrctjDTO.getKsjhbh(), ksrctjDTO.getCcm(), kdbhList, bzhkcids);
        List<KsrctjDatasItemVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ksrckcs)) {
            Map<String, List<KsRcRsDO>> map = new HashMap<>();
            for (KsRcRsDO ksrckc : ksrckcs) {
                List<KsRcRsDO> value = new ArrayList<>();
                if (map.containsKey(ksrckc.getWlkcbh())) {
                    value.addAll(map.get(ksrckc.getWlkcbh()));
                }
                value.add(ksrckc);
                map.put(ksrckc.getWlkcbh(), value);
            }
            for (String key : map.keySet()) {
                KsrctjDatasItemVO ksrctjDatasItemVO = new KsrctjDatasItemVO();
                List<KsRcRsDO> ksRcRsDOS = map.get(key);
                // 总人数
                int zrs = 0;
                int yrcrs = 0;
                int wrcrs = 0;
                int dqdrs = 0;
                for (KsRcRsDO ksRcRsDO : ksRcRsDOS) {
                    zrs += ksRcRsDO.getRs();
                    if (StringUtil.equals(ksRcRsDO.getSfrc(), "1")) {
                        yrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "0")) {
                        wrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "9")) {
                        dqdrs = ksRcRsDO.getRs();
                    }
                }
                // TODO 也许需要考虑考场名称为空时的情况
                ksrctjDatasItemVO.setCsbh(ksRcRsDOS.get(0).getWlkcbh());
                ksrctjDatasItemVO.setCsmc(ksRcRsDOS.get(0).getKcmc());
                ksrctjDatasItemVO.setWlcsmc(ksRcRsDOS.get(0).getWlkcmc());
                ksrctjDatasItemVO.setZrs(zrs);
                ksrctjDatasItemVO.setYrcrs(yrcrs);
                ksrctjDatasItemVO.setWrcrs(wrcrs);
                ksrctjDatasItemVO.setDqdrs(dqdrs);
                ksrctjDatasItemVO.setKcbh(ksRcRsDOS.get(0).getKcbh());
                list.add(ksrctjDatasItemVO);
            }
        }
        KsrctjVO ksrctjVO = new KsrctjVO();
        ksrctjVO.setDatas(list);
        return ksrctjVO;
    }

    @Override
    public KshyqkVO kshyqkcx(String ksjhbh, String ccm) {
        return modelMapper.kshyqkcx(ksjhbh, ccm);
    }

    @Override
    public SjsbTjxxDO getSjsbTjxx(String ksjhbh, String ccm) {
        return modelMapper.getSjsbTjxx(ksjhbh, ccm);
    }

    @Override
    public int updateByExampleSelective(KsKsrcxx ksKsrcxx, Example example) {
        ksKsrcxx.setUpdateTime(DateUtil.getCurrentDT());

        ksKsrcxx.setSbzt("1");  // 上报状态
        ksKsrcxx.setTbzt("0");  // 同步状态

        return modelMapper.updateByExampleSelective(ksKsrcxx, example);
    }

    @Override
    public KsrcsjsclbVO ksrcsjsclb(KsrcsjsclbDTO dto) {
        Example example = new Example(KsKsrcxx.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", dto.getKsjh());
        criteria.andEqualTo("ccm", dto.getKscc());
        if (StringUtil.isNotBlank(dto.getXm())) {
            criteria.andLike("ksxm", dto.getXm());
        }
        if (StringUtil.isNotBlank(dto.getZjhm())) {
            criteria.andLike("sfzh", dto.getZjhm());
        }
        if (StringUtil.isNotBlank(dto.getSbzt())) {
            Example.Criteria and = example.and();
            and.andLike("sbzt", dto.getSbzt());
            if (StringUtil.equals(dto.getSbzt(), "0")) {
                and.orIsNull("sbzt");
            }
        }

        List<KsKsrcxx> ksKsrcxxes = modelMapper.selectByExample(example);

        List<KsrcsjscItemVO> ksrcsjscList = new ArrayList<>();
        for (KsKsrcxx ksKsrcxx : ksKsrcxxes) {
            KsrcsjscItemVO ksrcsjscItemVO = new KsrcsjscItemVO();
            ksrcsjscItemVO.setXm(ksKsrcxx.getKsxm());
            ksrcsjscItemVO.setZkzh(ksKsrcxx.getZkzh());
            ksrcsjscItemVO.setZjhm(HsUtils.EncryptionSFZH(ksKsrcxx.getSfzh()));

            if (StringUtil.isNotBlank(ksKsrcxx.getRgyzjg())) {
                if (StringUtil.equals(ksKsrcxx.getRgyzjg(), "1")) {
                    ksrcsjscItemVO.setYzfs("人工验证");
                    ksrcsjscItemVO.setHyjg(SfhySftgEnum.TG.getDesc());
                } else if (StringUtil.equals(ksKsrcxx.getRgyzjg(), "0")) {
                    ksrcsjscItemVO.setYzfs("人工验证");
                    ksrcsjscItemVO.setHyjg(SfhySftgEnum.WTG.getDesc());
                }
            } else if (StringUtil.isNotBlank(ksKsrcxx.getYzfs())) {
                String yzfs = ksKsrcxx.getYzfs();
                if (StringUtil.isNotBlank(yzfs)) {
                    yzfs = yzfs.replace(SfhySftgEnum.SFZYZ.getCode(), SfhySftgEnum.SFZYZ.getDesc() + "-")
                            .replace(SfhySftgEnum.RLYZ.getCode(), SfhySftgEnum.RLYZ.getDesc() + "-")
                            .replace(SfhySftgEnum.ZWYZ.getCode(), SfhySftgEnum.ZWYZ.getDesc() + "-");
                    ksrcsjscItemVO.setYzfs(yzfs.substring(0, yzfs.length() - 1));
                } else {
                    ksrcsjscItemVO.setYzfs("-");
                }
                String yzjg = ksKsrcxx.getYzjg();
                if (StringUtil.isNotBlank(yzjg)) {
                    yzjg = yzjg.replace(SfhySftgEnum.TG.getCode(), SfhySftgEnum.TG.getDesc() + "-")
                            .replace(SfhySftgEnum.CY.getCode(), SfhySftgEnum.CY.getDesc() + "-")
                            .replace(SfhySftgEnum.SJKWTG.getCode(), SfhySftgEnum.SJKWTG.getDesc() + "-");
                    ksrcsjscItemVO.setHyjg(yzjg.substring(0, yzjg.length() - 1));
                } else {
                    ksrcsjscItemVO.setHyjg("-");
                }
            }
            if (StringUtil.equals(ksKsrcxx.getSfrc(), SfhySftgEnum.WRC.getCode())) {
                ksrcsjscItemVO.setYzfs("-");
                ksrcsjscItemVO.setHyjg(SfhySftgEnum.WRC.getDesc());
            }

            String sbzt = ksKsrcxx.getSbzt();
            if (StringUtil.isNotBlank(sbzt)) {
                ksrcsjscItemVO.setScjg(KsrcxxSbztEnum.get(sbzt).getDesc());
            } else {
                ksrcsjscItemVO.setScjg("-");
            }
            String tbzt = ksKsrcxx.getTbzt();
            if(StringUtils.isNotBlank(tbzt)){
                ksrcsjscItemVO.setTbjg(KsrcxxSbztEnum.get(tbzt).getDesc());
            }else{
                ksrcsjscItemVO.setTbjg("-");
            }
            ksrcsjscList.add(ksrcsjscItemVO);
        }

        KsrcsjsclbVO ksrcsjsclbVO = new KsrcsjsclbVO();
        ksrcsjsclbVO.setKsrcsjscList(ksrcsjscList);
        return ksrcsjsclbVO;
    }

    @Override
    public void batchUpdateTbzt(List<KsKsrcxx> ksrcxxList) {
        modelMapper.batchUpdateTbzt(ksrcxxList);
    }

    @Override
    public void ksrcxxcsh(String ksjhbh) {
        modelMapper.initByKsjhbh(ksjhbh);
    }

    @Override
    public KsrcZeroLbVO ksrcZeroLb(KsrcZeroLbDTO dto) {
        KsrcZeroLbVO result = new KsrcZeroLbVO();
        Page<Object> page = new Page<>();
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<ZeroKcItemVO> zeroKcItemVOS = modelMapper.ksrcZeroLb(dto);
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            result.setTotalRows((int) page.getTotal());
        }
        result.setZeroKcList(zeroKcItemVOS);

        if (zeroKcItemVOS.isEmpty()) {
            return result;
        }
        List<String> bzhkcids = zeroKcItemVOS.stream().map(ZeroKcItemVO::getCsdm).collect(Collectors.toList());

        Map<String, String> kcmcMap = ksKcxxService.getKcmcMap(dto.getKsjhbh(), bzhkcids);

        for (ZeroKcItemVO zeroKcItemVO : zeroKcItemVOS) {
            zeroKcItemVO.setKcmc(kcmcMap.get(zeroKcItemVO.getCsdm()));
        }
        return result;
    }

    @Override
    public void ksrcZeroNotice(KsrcZeroNoticeDTO dto) {
        Example emKc = new Example(KsKcxx.class);
        emKc.createCriteria()
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andIn("bzhkcid", dto.getKcidList());
        emKc.setDistinct(true);
        List<KsKcxx> kdKcxxes = ksKcxxService.selectListByExample(emKc);
        sendDataMissingToKc(dto.getKsjhbh(), dto.getCcm(), kdKcxxes);
    }

    @Override
    public RcwbsjLbVO rcwbsjLb(RcwbsjLbDTO dto) {
        RcwbsjLbVO result = new RcwbsjLbVO();

        Page<Object> page = new Page<>();
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<RcwbsjItemVO> rcwbsjItemVOS = modelMapper.rcwbsjLb(dto);
        result.setRcwbsjList(rcwbsjItemVOS);

        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            result.setTotalRows((int) page.getTotal());
        }
        return result;
    }

    @Override
    public void ydzdsbzsNotice(YdzdsbzsNoticeDTO dto) {
        Map<String, String> sbzxztMap = sbSbxxService.zxztByXlhs(dto.getSbList());
        if (sbzxztMap == null || sbzxztMap.isEmpty()) {
            throw new BusinessException("设备状态查询失败");
        }

        List<String> zxsbxlhList = sbzxztMap.entrySet().stream().filter(entry -> StringUtils.equals(entry.getValue(), "1")).map(Map.Entry::getKey).collect(Collectors.toList());
        if (zxsbxlhList.isEmpty()) {
            throw new BusinessException("无在线设备");
        }


        Example emRcxx = new Example(KsKsrcxx.class);
        emRcxx.createCriteria()
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andIn("sbxlh", zxsbxlhList);
        emRcxx.setDistinct(true);
        emRcxx.selectProperties("ksjhbh", "ccm", "kdbh", "bzhkdid", "kcbh", "ljkcbh", "sbxlh");
        List<KsKsrcxx> ksKsrcxxes = modelMapper.selectByExample(emRcxx);
        if (ksKsrcxxes.size() > 0) {
            Map<String, List<KsKsrcxx>> ksKsrcxxMap = ksKsrcxxes.stream().collect(Collectors.groupingBy(KsKsrcxx::getBzhkdid));
            ksKsrcxxMap.forEach((kdbh, rcxxList) -> {
                JSONObject dataJson = new JSONObject();
                dataJson.put("ksbh", dto.getKsjhbh());
                dataJson.put("ccm", dto.getCcm());
                dataJson.put("kddm", kdbh);
                for (KsKsrcxx ksKsrcxx : rcxxList) {
                    JSONArray kclb = new JSONArray();
                    JSONObject kcxxJson = new JSONObject();
                    kcxxJson.put("kch", ksKsrcxx.getKcbh());
                    kcxxJson.put("ljkcbh", ksKsrcxx.getLjkcbh());
                    kclb.add(kcxxJson);
                    dataJson.put("kclb", kclb);
                    unifyAccessUtils.sendMessage(ksKsrcxx.getSbxlh(), dataJson, "ACTIVE_BZKS_RCZS");
                }
            });
        }
    }

    @Override
    public KsrctjljkcVO ksrctjljkc(KsrctjljkcDTO dto) {
        List<String> kdbhList = dto.getCsbh() == null ? null : Collections.singletonList(dto.getCsbh());
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(dto.getCsbh(), dto.getCslx());
        List<KsLjkcRcRsDO> ksrckcs = modelMapper.cxljkcrcrs(dto.getKsjhbh(), dto.getCcm(), kdbhList, bzhkcids);
        List<KsrctjljkcDatasItemVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ksrckcs)) {
            Map<String, List<KsLjkcRcRsDO>> map = new HashMap<>();
            for (KsLjkcRcRsDO ksrckc : ksrckcs) {
                List<KsLjkcRcRsDO> value = new ArrayList<>();
                if (map.containsKey(ksrckc.getLjkcbh())) {
                    value.addAll(map.get(ksrckc.getLjkcbh()));
                }
                value.add(ksrckc);
                map.put(ksrckc.getLjkcbh(), value);
            }
            for (String key : map.keySet()) {
                KsrctjljkcDatasItemVO ksrctjDatasItemVO = new KsrctjljkcDatasItemVO();
                List<KsLjkcRcRsDO> ksRcRsDOS = map.get(key);
                // 总人数
                int zrs = 0;
                int yrcrs = 0;
                int wrcrs = 0;
                int dqdrs = 0;
                for (KsLjkcRcRsDO ksRcRsDO : ksRcRsDOS) {
                    zrs += ksRcRsDO.getRs();
                    if (StringUtil.equals(ksRcRsDO.getSfrc(), "1")) {
                        yrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "0")) {
                        wrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "9")) {
                        dqdrs = ksRcRsDO.getRs();
                    }
                }
                // TODO 也许需要考虑考场名称为空时的情况
                ksrctjDatasItemVO.setLjkcbh(ksRcRsDOS.get(0).getLjkcbh());
                ksrctjDatasItemVO.setCsbh(ksRcRsDOS.get(0).getWlkcbh());
                ksrctjDatasItemVO.setCsmc(ksRcRsDOS.get(0).getKcmc());
                ksrctjDatasItemVO.setWlcsmc(ksRcRsDOS.get(0).getWlkcmc());
                ksrctjDatasItemVO.setZrs(zrs);
                ksrctjDatasItemVO.setYrcrs(yrcrs);
                ksrctjDatasItemVO.setWrcrs(wrcrs);
                ksrctjDatasItemVO.setDqdrs(dqdrs);
                ksrctjDatasItemVO.setKcbh(ksRcRsDOS.get(0).getKcbh());
                list.add(ksrctjDatasItemVO);
            }
        }
        KsrctjljkcVO ksrctjVO = new KsrctjljkcVO();
        ksrctjVO.setDatas(list);
        return ksrctjVO;
    }

    @Override
    public KsrcqkxqcxxqVO ksrcqkxqcxxq(KsrcqkxqcxxqDTO dto) {
        KsrcqkxqcxxqVO result = new KsrcqkxqcxxqVO();
        Example ksrcEg = new Example(KsKsrcxx.class);
        ksrcEg.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("ksh", dto.getKsh());
        List<KsKsrcxx> ksrcxxes = this.selectListByExample(ksrcEg);
        Example ksbmEg = new Example(KsBmxx.class);
        ksbmEg.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhid", dto.getKsjhbh())
                .andEqualTo("ksh", dto.getKsh());
//        ksbmEg.selectProperties("zp");
        List<KsBmxx> ksBmxxes = ksBmxxService.selectListByExample(ksbmEg);

        if (!ksrcxxes.isEmpty()) {
            KsKsrcxx ksrcxx = ksrcxxes.get(0);
            int type;
            if (StringUtils.isNotBlank(ksrcxx.getRcrlzp())) {
                if (ksrcxx.getRcrlzp().contains("/")) {
                    type = 1;
                } else {
                    type = 0;
                }
                TimePathVO timePathVO = XcDfsClient.timePath(2L, type, ksrcxx.getRcrlzp());
                if (!timePathVO.getFileList().isEmpty()) {
                    result.setXczp(filePathPrefix + timePathVO.getFileList().get(0).getUrl());
                }
            }
            if (StringUtils.isNotBlank(ksrcxx.getSfzp())) {
                if (ksrcxx.getSfzp().contains("/")) {
                    type = 1;
                } else {
                    type = 0;
                }
                TimePathVO _timePathVO = XcDfsClient.timePath(2L, type, ksrcxx.getSfzp());
                if (!_timePathVO.getFileList().isEmpty()) {
                    result.setSfzzp(filePathPrefix + _timePathVO.getFileList().get(0).getUrl());
                }
            }
            if (!ksBmxxes.isEmpty()) {
                KsBmxx ksBmxx = ksBmxxes.get(0);
                if (StringUtils.isNotBlank(ksBmxx.getZp())) {
                    if (ksBmxx.getZp().contains("/")) {
                        type = 1;
                    } else {
                        type = 0;
                    }
                    TimePathVO timePathVO = XcDfsClient.timePath(2L, type, ksBmxx.getZp());
                    if (!timePathVO.getFileList().isEmpty()) {
                        result.setCjzp(filePathPrefix + timePathVO.getFileList().get(0).getUrl());

                    }
                }
            }
        }
        return result;
    }

    @Override
    public KsrcqkxqdcVO ksrcqkxqdc(KsrcqkxqdcDTO dto) {
        StopWatch sw = new StopWatch("ksrcqkxqdc");
        sw.start("查询,加工数据");
        KsrcqkxqcxDTO ksrcqkxqcxDTO = new KsrcqkxqcxDTO();
        BeanUtils.copyProperties(dto, ksrcqkxqcxDTO);
        String ksjhbh = ksrcqkxqcxDTO.getKsjhbh();
        String csbh = ksrcqkxqcxDTO.getCsbh();
        String cslx = ksrcqkxqcxDTO.getCslx();
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(csbh, cslx);
//        List<KsKcxx> bzhkcListByKsjhbh = ksKcxxMapper.getBzhkcList(ksjhbh);
//        List<String> bzhkcidsByKsjhbh = bzhkcListByKsjhbh.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
//        bzhkcids.retainAll(bzhkcidsByKsjhbh);
        KsrcqkxqcxVO ksrcqkxqcxVO = new KsrcqkxqcxVO();
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderCol())) {
            ksrcqkxqcxDTO.setOrderCol("KCBH,ZWH_PX,CCM");
        }
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderType())) {
            ksrcqkxqcxDTO.setOrderType("ASC");
        }
        List<KsrcqkDatasItemVO> ksrcqkxqcxVOList = modelMapper.ksrcqkxqcx(ksrcqkxqcxDTO, bzhkcids);
        ksrcqkxqcxVOList.forEach(k -> {
            String sfzh = k.getSfzh();
            // 身份证号脱敏
            k.setSfzh(HsUtils.EncryptionSFZH(sfzh));
        });
        sw.stop();
// 创建临时目录
        String dateStr = DateUtil.getCurrentDate("yyyy-MM-dd_HHmmss");
        String folderPath = generatePath + File.separator + "ksrcqkqk" + File.separator + ksjhbh + File.separator + dateStr;
        Path path = Paths.get(folderPath);
        try {
            Files.createDirectories(path);
        } catch (IOException e) {
            throw new RuntimeException("创建临时目录失败：" + folderPath, e);
        }

        String zipFilePath = folderPath + ".zip";
        List<String> fileList = new ArrayList<>();
        try {
            // 导出Excel文件
            sw.start("导出Excel文件");
            String excelPath = exportKsrcqkxqExcel(folderPath, ksrcqkxqcxVOList);
//            fileList.add(excelPath);
            sw.stop();

            // 获取不为空的照片文件
            sw.start("获取照片文件");
            Map<String, String> fileIdToNameMap = new HashMap<>();
            Map<String, String> fileIdToPathMap = new HashMap<>();
            List<String> photoFiles = exportKsrcqkxqPhoto(folderPath, dto.getKsjhbh(), dto.getCcm(), fileIdToNameMap, fileIdToPathMap, ksrcqkxqcxVOList);
            fileList.addAll(photoFiles);

            // 下载照片文件并重命名
            for (String fileId : photoFiles) {
                String fileName = fileIdToNameMap.get(fileId);
                String filePath = fileIdToPathMap.get(fileId);
                if (StringUtil.isNotBlank(filePath)) {
                    String newFilePath = folderPath + File.separator + fileName;
                    URL url = new URL(filePath);
                    File file = new File(newFilePath);
                    FileUtils.copyURLToFile(url, file);
                    log.info("下载文件成功，fileId:{} filePath:{} newFilePath:{}", fileId, filePath, newFilePath);
                }
            }

            sw.stop();
            sw.start("压缩文件");
            // 压缩文件
            String zip = CompressUtil.zip(folderPath, "");
            if (StringUtil.isBlank(zip)) {
                throw new BusinessException("打包失败");
            }
            log.info("压缩文件成功，zipFilePath:{}", zipFilePath);
            sw.stop();
            sw.start("上传压缩包");
            // 上传压缩包
            DateTime expireTime = DateUtil.offsetHour(new Date(), 2);
            UploadAttachmentReturnUrlVO uploadAttachmentReturnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(zipFilePath, expireTime, null);
            sw.stop();
            // 返回上传后的附件路径
            KsrcqkxqdcVO result = new KsrcqkxqdcVO();
            result.setDclj(uploadAttachmentReturnUrlVO.getAttachmentUrl());
            log.info(sw.prettyPrint());
            return result;

        } catch (IOException e) {
            log.error("文件处理或压缩失败，zipFilePath:{} fileList:{}，异常信息：{}", zipFilePath, fileList, e);
            throw new BusinessException("打包失败");
        } finally {
            // 删除临时文件
            for (String filePath : fileList) {
                try {
                    Files.deleteIfExists(Paths.get(filePath));
                } catch (IOException e) {
                    log.warn("删除临时文件失败：{}", filePath, e);
                }
            }
            try {
                Files.deleteIfExists(Paths.get(zipFilePath));
            } catch (IOException e) {
                log.warn("删除ZIP文件失败：{}", zipFilePath, e);
            }
            // 删除临时目录（递归删除）
            try {
                deleteDirectoryRecursively(Paths.get(folderPath));
            } catch (IOException e) {
                log.warn("递归删除临时目录失败：{}", folderPath, e);
            }
        }
    }

    @Override
    public UploadValidateInfoResultVO uploadValidateInfoToProvince(String ksjhbh, String ccm) {
        return null;
    }

    private List<String> exportKsrcqkxqPhoto(String folderPath, String ksjhbh, String ccm, Map<String, String> fileIdToNameMap, Map<String, String> fileIdToPathMap, List<KsrcqkDatasItemVO> ksrcqkxqcxVOList) {
        List<String> photoFiles = new ArrayList<>();

        List<String> photeFileIds = new ArrayList<>();
        List<String> ksh = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ksrcqkxqcxVOList)){
            ksh = ksrcqkxqcxVOList.stream().map(KsrcqkDatasItemVO::getKsh).collect(Collectors.toList());
        }

        Example em = new Example(KsKsrcxx.class);
        em.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        if (StringUtil.isNotBlank(ccm)){
            em.and().andEqualTo("ccm", ccm);
        }
        if (!CollectionUtils.isEmpty(ksh)){
            em.and().andIn("ksh", ksh);
        }
        List<KsKsrcxx> ksKsrcxxes = this.selectListByExample(em);
//        List<String> ksh = ksKsrcxxes.stream().map(KsKsrcxx::getKsh).collect(Collectors.toList());
        Example ksbmEg = new Example(KsBmxx.class);
        ksbmEg.createCriteria()
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhid", ksjhbh)
                .andIn("ksh", ksh);
//        ksbmEg.selectProperties("zp");
        List<KsBmxx> ksBmxxes = ksBmxxService.selectListByExample(ksbmEg);


        Map<String, String> kshZhzhMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ksKsrcxxes)) {
            for (KsKsrcxx ksKsrcxx : ksKsrcxxes) {
                kshZhzhMap.put(ksKsrcxx.getKsh(), ksKsrcxx.getZkzh());
                String xczp = ksKsrcxx.getRcrlzp();
                if (StringUtil.isNotBlank(xczp) && isShuaLian(ksKsrcxx)) {
                    fileIdToNameMap.put(xczp, StringUtil.join( File.separator, "现场照片", ksKsrcxx.getCcm(), StringUtil.join("_", ksKsrcxx.getCcm(), ksKsrcxx.getZkzh(), "xczp", ".jpg")) );
                    photeFileIds.add(xczp);
                }
                String sfzzp = ksKsrcxx.getSfzp();
                if (StringUtil.isNotBlank(sfzzp)) {
                    fileIdToNameMap.put(sfzzp, StringUtil.join(File.separator, "身份证照片", ksKsrcxx.getCcm(), StringUtil.join("_", ksKsrcxx.getCcm(), ksKsrcxx.getZkzh(), "sfzzp", ".jpg")) );
                    photeFileIds.add(sfzzp);
                }
            }
        }
        if (!CollectionUtils.isEmpty(ksBmxxes)) {
            for (KsBmxx ksBmxx : ksBmxxes) {
                String zp = ksBmxx.getZp();
                if (StringUtil.isNotBlank(zp)) {
                    fileIdToNameMap.put(zp, StringUtil.join(File.separator, "报名照片" , StringUtil.join("_", kshZhzhMap.getOrDefault(ksBmxx.getKsh(), ksBmxx.getKsh()), "bmzp", ".jpg"))) ;
                    photeFileIds.add(zp);
                }
            }
        }

        GetAttachUrlDTO getAttachUrlDTO = new GetAttachUrlDTO();
        getAttachUrlDTO.setFileIds(photeFileIds);
        GetAttachUrlVO attachUrl = attachmentHandler.getAttachUrl(getAttachUrlDTO);
        for (AttachFileVoItemVO attachFileVoItemVO : attachUrl.getAttachFileVoList()) {
            fileIdToPathMap.put(attachFileVoItemVO.getFile(), attachFileVoItemVO.getUrl());
        }

        return photeFileIds;
    }

    private boolean isShuaLian(KsKsrcxx ksKsrcxx) {
        if(!StringUtil.equals(SfhySftgEnum.YRC.getCode(), ksKsrcxx.getSfrc())){
            return false;
        }
        if (StringUtil.isBlank(ksKsrcxx.getRgyzjg())){
            return true;
        }
        return false;
    }

    // 递归删除目录方法
    public static void deleteDirectoryRecursively(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            return;
        }
        Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.deleteIfExists(file);
                return FileVisitResult.CONTINUE;
            }
            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.deleteIfExists(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }
    private String exportKsrcqkxqExcel(String folderPath, List<KsrcqkDatasItemVO> ksrcqkxqcxVOList) {
        List<KsrcqkxqTemplate> collect = ksrcqkxqcxVOList.stream().map(item -> {
            KsrcqkxqTemplate ksrcqkxqTemplate = new KsrcqkxqTemplate();
            ksrcqkxqTemplate.setCcmc(item.getCcmc());
            ksrcqkxqTemplate.setKch(item.getKcbh());
            ksrcqkxqTemplate.setZwh(item.getZwh());
            ksrcqkxqTemplate.setKsxm(item.getKsxm());
            ksrcqkxqTemplate.setZkzh(item.getZkzh());
            String sbfs = convertSbfs(item.getSbfs());
            ksrcqkxqTemplate.setSbfs(sbfs);
            String sfrc = convertSfrc(item.getSfrc());
            ksrcqkxqTemplate.setRczt(sfrc);
            ksrcqkxqTemplate.setRcsj(item.getRcsj());
            ksrcqkxqTemplate.setXsd(item.getXsd());
            return ksrcqkxqTemplate;
        }).collect(Collectors.toList());
        String fileName = folderPath+ File.separator + "考生入场信息" + DateUtil.getCurrentDate("yyyy-MM-dd_HHmmss") + ".xlsx";
        // 按场次分组,写到同一个文件的不同sheet
        Map<String, List<KsrcqkxqTemplate>> groupedData = collect.stream()
                .collect(Collectors.groupingBy(KsrcqkxqTemplate::getCcmc));
//        EasyExcel.write(fileName, KsrcqkxqTemplate.class).sheet().doWrite(collect);
        // 创建ExcelWriter
        ExcelWriter build = EasyExcel.write(fileName, KsrcqkxqTemplate.class).build();
        // 遍历分组数据
        for (Map.Entry<String, List<KsrcqkxqTemplate>> entry : groupedData.entrySet()) {
            String sheetName = entry.getKey();
            List<KsrcqkxqTemplate> dataList = entry.getValue();
            // 创建Sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            // 写入数据
            build.write(dataList, writeSheet);
        }
        build.finish();
        return fileName;
    }

    private String convertSfrc(String sfrc) {
//        0缺考/1已入场/9待确定
        if (StringUtils.isBlank(sfrc)) {
            return "";
        }
        if (StringUtil.equals(sfrc, "0")) {
            return "缺考";
        } else if (StringUtil.equals(sfrc, "1")) {
            return "已入场";
        } else if (StringUtil.equals(sfrc, "9")) {
            return "待确定";
        } else {
            return "";
        }
    }

    private String convertSbfs(String sbfs) {
        // 上报方式 0 刷脸 1 手动
        if (StringUtils.isBlank(sbfs)) {
            return "";
        }
        if (StringUtil.equals(sbfs, "0")) {
            return "刷脸";
        } else if (StringUtil.equals(sbfs, "1")) {
            return "手动";
        } else {
            return "";
        }
    }


    private void sendDataMissingToKc(String ksjhbh, String ccm, List<KsKcxx> kdKcxxes) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(kdKcxxes)) {
            return;
        }
        List<String> kcidList = kdKcxxes.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
        Example emDistribute = new Example(KsKssjDistributeStatus.class);
        emDistribute.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("type", "1")
                .andIn("bzhkcbh", kcidList);
        emDistribute.selectProperties("bzhkcbh", "ydzdDownloadSn");
        emDistribute.setDistinct(true);
        List<KsKssjDistributeStatus> ksDistributeStatuses = ksKssjDistributeStatusService.selectListByExample(emDistribute);
        Map<String, String> kczdXlhMap = new HashMap<>(); // 考场移动终端sn映射
        for (KsKssjDistributeStatus ksDistributeStatus : ksDistributeStatuses) {
            kczdXlhMap.put(ksDistributeStatus.getBzhkcbh(), ksDistributeStatus.getYdzdDownloadSn());
        }

        for (KsKcxx kdKcxx : kdKcxxes) {
            JSONArray qssjArray = new JSONArray();
            Example em = new Example(KsKsrcxx.class);
            em.selectProperties("zkzh", "kcbh", "ljkcbh");
            em.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("bzhkdid", kdKcxx.getBzhkdid())
                    .andEqualTo("kcbh", kdKcxx.getKcbh())
                    .andEqualTo("sfrc", "9");
            List<KsKsrcxx> list = modelMapper.selectByExample(em);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                // 准考证号按kcbh_ljkcbh分组
                Map<String, List<String>> zkzhMap = new HashMap<>();
                for (KsKsrcxx ksKsrcxx : list) {
                    String key = ksKsrcxx.getKcbh() + "_" + ksKsrcxx.getLjkcbh();
                    zkzhMap.putIfAbsent(key, new ArrayList<>());
                    zkzhMap.get(key).add(ksKsrcxx.getZkzh());
                }

                zkzhMap.forEach((kcbh_ljkcbh, zkzhList) -> {
                    String[] kcbh_ljkcbhArr = kcbh_ljkcbh.split("_");
                    String kcbh = kcbh_ljkcbhArr[0];
                    String ljkcbh = kcbh_ljkcbhArr[1];
                    JSONObject dataJson = buildQssj(zkzhList, ksjhbh, ccm, kdKcxx.getBzhkdid(), kcbh, ljkcbh);
                    qssjArray.add(dataJson);
                });

                unifyAccessUtils.sendQssj(kczdXlhMap.get(kdKcxx.getBzhkcid()), qssjArray);

            }
        }
    }

    private JSONObject buildQssj(List<String> zkzhList, String ksjhbh, String ccm, String bzhkdid, String kcbh, String ljkcbh) {
        JSONObject json = new JSONObject();
        json.put("ksbh", ksjhbh);
        json.put("ccm", ccm);
        json.put("kddm", bzhkdid);
        json.put("kch", kcbh);
        json.put("ljkcbh", ljkcbh);
        JSONArray jsonArray = new JSONArray();
        for (String zkzh : zkzhList) {
            JSONObject mjson = new JSONObject();
            mjson.put("zkzh", zkzh);
            jsonArray.add(mjson);
        }
        json.put("kslb", jsonArray);
        return json;
    }
}