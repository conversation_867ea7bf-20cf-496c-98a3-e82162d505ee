package com.xcwlkj.identityverify.provincePlatform.request.items;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetValidateStuEnterItem implements Serializable {

    /** 上报时间yyyy-MM-dd HH:mm:ss */
    private String sbsj;

    /** 准考证号 */
    private String zkzh;

    /** 考点代码 */
    private String kddm;

    /** 考场号 */
    private String kch;

    /** 入场状态 0-人工审核不通过，1-人工审核通过，2-缺考 */
    private String rczt;

    /** 相似度 */
    private String xsd;

    /** 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场 */
    private String sjyxj;
}
