package com.xcwlkj.identityverify.third.superior.http.model.enums;

public enum PlatImportTypeEnum {
    provinceImport(0, "省端导入"),
    dbfImport(1, "dbf导入"),
    excelImport(2, "excel");

    private Integer impotyType;
    private String name;

    PlatImportTypeEnum(Integer impotyType, String name) {
        this.impotyType = impotyType;
        this.name = name;
    }

    public Integer getImpotyType() {
        return impotyType;
    }

    public String getName() {
        return name;
    }
}
