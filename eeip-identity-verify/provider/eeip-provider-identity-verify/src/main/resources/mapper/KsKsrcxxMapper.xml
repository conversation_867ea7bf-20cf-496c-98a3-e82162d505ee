<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.identityverify.mapper.KsKsrcxxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.identityverify.model.domain.KsKsrcxx">
        <id column="ksrcbh" jdbcType="VARCHAR" property="ksrcbh" />
        <result column="ksjhbh" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="ccm" jdbcType="VARCHAR" property="ccm" />
        <result column="zkzh" jdbcType="VARCHAR" property="zkzh" />
        <result column="yzfs" jdbcType="VARCHAR" property="yzfs" />
        <result column="yzjg" jdbcType="VARCHAR" property="yzjg" />
        <result column="sfrc" jdbcType="VARCHAR" property="sfrc" />
        <result column="rcsj" jdbcType="VARCHAR" property="rcsj" />
        <result column="bzhkdid" jdbcType="VARCHAR" property="bzhkdid" />
        <result column="bzhkcid" jdbcType="VARCHAR" property="bzhkcid" />
        <result column="scztw" jdbcType="VARCHAR" property="scztw" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="sbzt" jdbcType="VARCHAR" property="sbzt" />
        <result column="sbsj" jdbcType="TIMESTAMP" property="sbsj" />
        <result column="tbzt" jdbcType="VARCHAR" property="tbzt" />
        <result column="gxzt" jdbcType="VARCHAR" property="gxzt" />
        <result column="kdbh" jdbcType="VARCHAR" property="kdbh" />
        <result column="kdmc" jdbcType="VARCHAR" property="kdmc" />
        <result column="kcbh" jdbcType="VARCHAR" property="kcbh" />
        <result column="kcmc" jdbcType="VARCHAR" property="kcmc" />
        <result column="ksxm" jdbcType="VARCHAR" property="ksxm" />
        <result column="zwh" jdbcType="VARCHAR" property="zwh" />
        <result column="zwh_px" jdbcType="BIGINT" property="zwhPx" />
        <result column="kslxm" jdbcType="VARCHAR" property="kslxm" />
        <result column="kslxmc" jdbcType="VARCHAR" property="kslxmc" />
        <result column="kmmc" jdbcType="VARCHAR" property="kmmc" />
        <result column="kmdm" jdbcType="VARCHAR" property="kmdm" />
        <result column="sfzh" jdbcType="VARCHAR" property="sfzh" />
        <result column="bj" jdbcType="VARCHAR" property="bj" />
        <result column="bmddm" jdbcType="VARCHAR" property="bmddm" />
        <result column="rcsjly" jdbcType="VARCHAR" property="rcsjly" />
        <result column="rcsjfz" jdbcType="VARCHAR" property="rcsjfz" />
        <result column="ksh" jdbcType="VARCHAR" property="ksh" />
        <result column="rgyzjg" jdbcType="VARCHAR" property="rgyzjg" />
        <result column="cslxdm" jdbcType="VARCHAR" property="cslxdm" />
        <result column="bkkl" jdbcType="VARCHAR" property="bkkl" />
        <result column="ljkcbh" jdbcType="VARCHAR" property="ljkcbh" />
        <result column="rcrlzp" jdbcType="VARCHAR" property="rcrlzp" />
        <result column="sfzp" jdbcType="VARCHAR" property="sfzp" />
        <result column="sbxlh" jdbcType="VARCHAR" property="sbxlh" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        ksrcbh,
        ksjhbh,
        ccm,
        zkzh,
        yzfs,
        yzjg,
        sfrc,
        rcsj,
        bzhkdid,
        bzhkcid,
        scztw,
        create_time,
        update_time,
        sbzt,
        sbsj,
        tbzt,
        gxzt,
        kdbh,
        kdmc,
        kcbh,
        kcmc,
        ksxm,
        zwh,
        zwh_px,
        kslxm,
        kslxmc,
        kmmc,
        kmdm,
        sfzh,
        bj,
        bmddm,
        rcsjly,
        rcsjfz,
        ksh,
        rgyzjg,
        cslxdm,
        bkkl,
        ljkcbh,
        rcrlzp,
        sfzp,
        sbxlh

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="ksrcbh != null and ksrcbh != ''">
            AND ksrcbh = #{ksrcbh,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND ksjhbh = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND ccm = #{ccm,jdbcType=VARCHAR}
        </if>
        <if test="zkzh != null and zkzh != ''">
            AND zkzh = #{zkzh,jdbcType=VARCHAR}
        </if>
        <if test="yzfs != null and yzfs != ''">
            AND yzfs = #{yzfs,jdbcType=VARCHAR}
        </if>
        <if test="yzjg != null and yzjg != ''">
            AND yzjg = #{yzjg,jdbcType=VARCHAR}
        </if>
        <if test="sfrc != null and sfrc != ''">
            AND sfrc = #{sfrc,jdbcType=VARCHAR}
        </if>
        <if test="rcsj != null and rcsj != ''">
            AND rcsj = #{rcsj,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdid != null and bzhkdid != ''">
            AND bzhkdid = #{bzhkdid,jdbcType=VARCHAR}
        </if>
        <if test="bzhkcid != null and bzhkcid != ''">
            AND bzhkcid = #{bzhkcid,jdbcType=VARCHAR}
        </if>
        <if test="scztw != null and scztw != ''">
            AND scztw = #{scztw,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="sbzt != null and sbzt != ''">
            AND sbzt = #{sbzt,jdbcType=VARCHAR}
        </if>
        <if test="sbsj != null and sbsj != ''">
            AND sbsj = #{sbsj,jdbcType=TIMESTAMP}
        </if>
        <if test="tbzt != null and tbzt != ''">
            AND tbzt = #{tbzt,jdbcType=VARCHAR}
        </if>
        <if test="gxzt != null and gxzt != ''">
            AND gxzt = #{gxzt,jdbcType=VARCHAR}
        </if>
        <if test="kdbh != null and kdbh != ''">
            AND kdbh = #{kdbh,jdbcType=VARCHAR}
        </if>
        <if test="kdmc != null and kdmc != ''">
            AND kdmc = #{kdmc,jdbcType=VARCHAR}
        </if>
        <if test="kcbh != null and kcbh != ''">
            AND kcbh = #{kcbh,jdbcType=VARCHAR}
        </if>
        <if test="kcmc != null and kcmc != ''">
            AND kcmc = #{kcmc,jdbcType=VARCHAR}
        </if>
        <if test="ksxm != null and ksxm != ''">
            AND ksxm = #{ksxm,jdbcType=VARCHAR}
        </if>
        <if test="zwh != null and zwh != ''">
            AND zwh = #{zwh,jdbcType=VARCHAR}
        </if>
        <if test="zwhPx != null and zwhPx != ''">
            AND zwh_px = #{zwhPx,jdbcType=BIGINT}
        </if>
        <if test="kslxm != null and kslxm != ''">
            AND kslxm = #{kslxm,jdbcType=VARCHAR}
        </if>
        <if test="kslxmc != null and kslxmc != ''">
            AND kslxmc = #{kslxmc,jdbcType=VARCHAR}
        </if>
        <if test="kmmc != null and kmmc != ''">
            AND kmmc = #{kmmc,jdbcType=VARCHAR}
        </if>
        <if test="kmdm != null and kmdm != ''">
            AND kmdm = #{kmdm,jdbcType=VARCHAR}
        </if>
        <if test="sfzh != null and sfzh != ''">
            AND sfzh = #{sfzh,jdbcType=VARCHAR}
        </if>
        <if test="bj != null and bj != ''">
            AND bj = #{bj,jdbcType=VARCHAR}
        </if>
        <if test="bmddm != null and bmddm != ''">
            AND bmddm = #{bmddm,jdbcType=VARCHAR}
        </if>
        <if test="rcsjly != null and rcsjly != ''">
            AND rcsjly = #{rcsjly,jdbcType=VARCHAR}
        </if>
        <if test="rcsjfz != null and rcsjfz != ''">
            AND rcsjfz = #{rcsjfz,jdbcType=VARCHAR}
        </if>
        <if test="ksh != null and ksh != ''">
            AND ksh = #{ksh,jdbcType=VARCHAR}
        </if>
        <if test="rgyzjg != null and rgyzjg != ''">
            AND rgyzjg = #{rgyzjg,jdbcType=VARCHAR}
        </if>
        <if test="cslxdm != null and cslxdm != ''">
            AND cslxdm = #{cslxdm,jdbcType=VARCHAR}
        </if>
        <if test="bkkl != null and bkkl != ''">
            AND bkkl = #{bkkl,jdbcType=VARCHAR}
        </if>
        <if test="ljkcbh != null and ljkcbh != ''">
            AND ljkcbh = #{ljkcbh,jdbcType=VARCHAR}
        </if>
        <if test="rcrlzp != null and rcrlzp != ''">
            AND rcrlzp = #{rcrlzp,jdbcType=VARCHAR}
        </if>
        <if test="sfzp != null and sfzp != ''">
            AND sfzp = #{sfzp,jdbcType=VARCHAR}
        </if>
        <if test="sbxlh != null and sbxlh != ''">
            AND sbxlh = #{sbxlh,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="ksrcbh != null ">
            ksrcbh = #{ksrcbh,jdbcType=VARCHAR},
        </if>
        <if test="ksjhbh != null ">
            ksjhbh = #{ksjhbh,jdbcType=VARCHAR},
        </if>
        <if test="ccm != null ">
            ccm = #{ccm,jdbcType=VARCHAR},
        </if>
        <if test="zkzh != null ">
            zkzh = #{zkzh,jdbcType=VARCHAR},
        </if>
        <if test="yzfs != null ">
            yzfs = #{yzfs,jdbcType=VARCHAR},
        </if>
        <if test="yzjg != null ">
            yzjg = #{yzjg,jdbcType=VARCHAR},
        </if>
        <if test="sfrc != null ">
            sfrc = #{sfrc,jdbcType=VARCHAR},
        </if>
        <if test="rcsj != null ">
            rcsj = #{rcsj,jdbcType=VARCHAR},
        </if>
        <if test="bzhkdid != null ">
            bzhkdid = #{bzhkdid,jdbcType=VARCHAR},
        </if>
        <if test="bzhkcid != null ">
            bzhkcid = #{bzhkcid,jdbcType=VARCHAR},
        </if>
        <if test="scztw != null ">
            scztw = #{scztw,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="sbzt != null ">
            sbzt = #{sbzt,jdbcType=VARCHAR},
        </if>
        <if test="sbsj != null ">
            sbsj = #{sbsj,jdbcType=TIMESTAMP},
        </if>
        <if test="tbzt != null ">
            tbzt = #{tbzt,jdbcType=VARCHAR},
        </if>
        <if test="gxzt != null ">
            gxzt = #{gxzt,jdbcType=VARCHAR},
        </if>
        <if test="kdbh != null ">
            kdbh = #{kdbh,jdbcType=VARCHAR},
        </if>
        <if test="kdmc != null ">
            kdmc = #{kdmc,jdbcType=VARCHAR},
        </if>
        <if test="kcbh != null ">
            kcbh = #{kcbh,jdbcType=VARCHAR},
        </if>
        <if test="kcmc != null ">
            kcmc = #{kcmc,jdbcType=VARCHAR},
        </if>
        <if test="ksxm != null ">
            ksxm = #{ksxm,jdbcType=VARCHAR},
        </if>
        <if test="zwh != null ">
            zwh = #{zwh,jdbcType=VARCHAR},
        </if>
        <if test="zwhPx != null ">
            zwh_px = #{zwhPx,jdbcType=BIGINT},
        </if>
        <if test="kslxm != null ">
            kslxm = #{kslxm,jdbcType=VARCHAR},
        </if>
        <if test="kslxmc != null ">
            kslxmc = #{kslxmc,jdbcType=VARCHAR},
        </if>
        <if test="kmmc != null ">
            kmmc = #{kmmc,jdbcType=VARCHAR},
        </if>
        <if test="kmdm != null ">
            kmdm = #{kmdm,jdbcType=VARCHAR},
        </if>
        <if test="sfzh != null ">
            sfzh = #{sfzh,jdbcType=VARCHAR},
        </if>
        <if test="bj != null ">
            bj = #{bj,jdbcType=VARCHAR},
        </if>
        <if test="bmddm != null ">
            bmddm = #{bmddm,jdbcType=VARCHAR},
        </if>
        <if test="rcsjly != null ">
            rcsjly = #{rcsjly,jdbcType=VARCHAR},
        </if>
        <if test="rcsjfz != null ">
            rcsjfz = #{rcsjfz,jdbcType=VARCHAR},
        </if>
        <if test="ksh != null ">
            ksh = #{ksh,jdbcType=VARCHAR},
        </if>
        <if test="rgyzjg != null ">
            rgyzjg = #{rgyzjg,jdbcType=VARCHAR},
        </if>
        <if test="cslxdm != null ">
            cslxdm = #{cslxdm,jdbcType=VARCHAR},
        </if>
        <if test="bkkl != null ">
            bkkl = #{bkkl,jdbcType=VARCHAR},
        </if>
        <if test="ljkcbh != null ">
            ljkcbh = #{ljkcbh,jdbcType=VARCHAR}
        </if>
        <if test="rcrlzp != null ">
            rcrlzp = #{rcrlzp,jdbcType=VARCHAR}
        </if>
        <if test="sfzp != null ">
            sfzp = #{sfzp,jdbcType=VARCHAR}
        </if>
        <if test="sbxlh != null ">
            sbxlh = #{sbxlh,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>
    <select id="ksrcqkxqcx" resultType="com.xcwlkj.identityverify.model.vo.cxtj.KsrcqkDatasItemVO">
        SELECT DISTINCT jh.ksjhbh,jh.mc ksjhmc,rc.ccm,cc.ccmc,rc.kmdm,rc.kmmc,rc.kdbh,rc.kdmc,rc.kcbh,rc.kcmc,rc.zwh,
        rc.ksxm,rc.zkzh,rc.ksh,rc.sfzh,rc.sfrc,rc.rcsj,
        DATE_FORMAT(rc.update_time,'%Y-%m-%d %H:%i:%s') gxsj,
        (CASE WHEN rc.sfrc ='1' THEN  ( CASE WHEN rc.rgyzjg IS NULL OR rc.rgyzjg = '' THEN '0' ELSE '1' END )
        when rc.sfrc ='0'  then  ( CASE WHEN rc.rgyzjg IS NULL OR rc.rgyzjg = '' THEN null ELSE '1' END )  END) sfsd,
        /* 当 sbfs = '0' 时返回 rc.xsd，其余情况返回空 */
        (CASE
        WHEN rc.sfrc = 1
        AND (rc.rgyzjg IS NULL
        OR rc.rgyzjg = '')
        THEN rc.xsd
        ELSE null
        END) xsd,
        (CASE WHEN rc.sfrc = 1 THEN
            (CASE WHEN rc.rgyzjg IS NULL OR rc.rgyzjg = '' THEN '0' ELSE '1' END)
        ELSE '-' END)
        sbfs
        FROM ks_ksrcxx rc
        JOIN ks_ksjh jh ON rc.ksjhbh = jh.ksjhbh AND jh.SCZTW = '0'
        JOIN ks_kscc cc ON cc.ksjhbh = rc.ksjhbh AND cc.CCM = rc.CCM /*AND CC.KMM = RC.KMDM*/
        <where>
            rc.scztw = '0' AND rc.ksjhbh = #{dto.ksjhbh}
            <if test="dto.ccm != null and dto.ccm != ''">
                AND rc.ccm = #{dto.ccm}
            </if>
            <if test="bzhkcids != null and bzhkcids.size() > 0">
                AND rc.bzhkcid IN
                <foreach collection="bzhkcids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.sfqk != null and dto.sfqk != '' ">
                AND rc.sfrc = #{dto.sfqk}
            </if>
            <if test="dto.kcbh != null and dto.kcbh != '' ">
                AND rc.kcbh LIKE CONCAT('%',CONCAT(#{dto.kcbh},'%'))
            </if>
            <if test="dto.zkzh != null and dto.zkzh != '' ">
                AND rc.zkzh LIKE CONCAT('%',CONCAT(#{dto.zkzh},'%'))
            </if>
            <if test="dto.ksxm != null and dto.ksxm != '' ">
                AND rc.ksxm LIKE CONCAT('%',CONCAT(#{dto.ksxm},'%'))
            </if>
            <if test="dto.zwh != null and dto.zwh != '' ">
                AND rc.zwh = #{dto.zwh}
            </if>
            <if test="dto.sfzh != null and dto.sfzh != '' ">
                AND rc.sfzh LIKE CONCAT('%',CONCAT(#{dto.sfzh},'%'))
            </if>
            <if test="dto.bzhkdid != null and dto.bzhkdid != '' ">
                AND rc.bzhkdid = #{dto.bzhkdid}
            </if>
            <choose>
                <when test="dto.sbfs == '0'.toString()">
                    AND rc.sfrc IN ('0', '1') AND rc.rgyzjg IS NULL
                </when>
                <when test="dto.sbfs == '1'.toString()">
                    AND rc.sfrc IN ('0', '1') AND rc.rgyzjg IS NOT NULL
                </when>
            </choose>
            <if test="dto.ksh != null and dto.ksh != ''">
                AND rc.ksh = #{dto.ksh}
            </if>
        </where>
        <if test="dto.orderCol != null and dto.orderCol != ''">
            ORDER BY ${dto.orderCol}
        </if>
        <choose>
            <when test="dto.orderType == 'asc' ">
                ASC
            </when>
            <when test="dto.orderType == 'desc' ">
                DESC
            </when>
        </choose>
    </select>
    <select id="cxkcrcrs" resultType="com.xcwlkj.identityverify.model.dos.KsRcRsDO">
        SELECT max(rc.kcbh) kcbh,max(rc.kcmc) kcmc,max(rc.bzhkcid) wlkcbh,kcxx.bzhkcmc wlkcmc,rc.sfrc sfrc,count(*) rs
        FROM ks_ksrcxx rc
        INNER JOIN
            (SELECT
                kc.ksjhbh ksjhbh,kc.ccm ccm,kc.kcbh kcbh,kc.bzhkcid bzhkcid,kc.bzhkcmc bzhkcmc,kc.ljkcbh ljkcbh
            FROM
            ks_kcxx kc
            <where>
                <if test="ksjhbh != null and ksjhbh != ''">
                    AND kc.ksjhbh = #{ksjhbh}
                </if>
                <if test="ccm != null and ccm != ''">
                    AND kc.ccm = #{ccm}
                </if>
            </where>
            ) kcxx ON rc.ksjhbh = kcxx.ksjhbh AND rc.ccm = kcxx.ccm AND rc.kcbh = kcxx.kcbh AND rc.bzhkcid = kcxx.bzhkcid AND rc.ljkcbh = kcxx.ljkcbh
        WHERE 1=1
            <if test="ksjhbh != null and ksjhbh != ''">
                AND rc.ksjhbh = #{ksjhbh}
            </if>
            <if test="ccm != null and ccm != ''">
                AND rc.ccm = #{ccm}
            </if>
            <if test="bzhkcids != null and bzhkcids.size() > 0">
                AND rc.bzhkcid IN
                <foreach collection="bzhkcids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
                AND rc.scztw = '0'
        GROUP BY rc.bzhkcid,rc.sfrc
        ORDER BY kcbh,kcmc
    </select>

    <select id="kcksqk" resultType="com.xcwlkj.identityverify.model.dos.KcksqkDO">
        select rc.ljkcbh,rc.kcbh,rc.kcmc,rc.zwh,rc.zkzh,rc.ksxm,rc.sfrc,if(wg.id is null or wg.id = '','0','1') as wjzt
        from ks_ksrcxx rc
        left join ks_kswj_wjwgmd wg on rc.ksjhbh = wg.ksjh and rc.ccm = wg.kscc and rc.kmdm = wg.kmdm and rc.ljkcbh = wg.ljkch and rc.ksh = wg.ksh
        where rc.ksjhbh = #{ksjhbh}
        <if test="ccm != null and ccm != ''">
            and rc.ccm = #{ccm}
        </if>
        <if test="ljkcbhList != null and ljkcbhList.size() > 0">
            and rc.ljkcbh in
            <foreach collection="ljkcbhList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by rc.ljkcbh,rc.kcbh,rc.kcmc,rc.zwh_px
    </select>
    <select id="kcksqkTj" resultType="com.xcwlkj.identityverify.model.dos.KcksqkTjDO">
        select rc.bzhkcid,rc.ljkcbh,rc.kcbh,cj.jsmc,count(rc.ljkcbh) as kszrs,sum(IF(rc.sfrc = '1', 1, 0)) as ksrcrs,sum(IF(rc.sfrc = '0', 1, 0)) as ksqkrs,sum(IF(wg.id is not null, 1, 0)) as kswjrs
        from ks_ksrcxx rc
        left join ks_kswj_wjwgmd wg on rc.ksjhbh = wg.ksjh and rc.ccm = wg.kscc and rc.kmdm = wg.kmdm and rc.bzhkdid = wg.bzhkdid and rc.kcbh = wg.kcbh and rc.ljkcbh = wg.ljkch and rc.ksh = wg.ksh and wg.sczt = '0'
        left join cs_jsjbxx cj on rc.bzhkcid = cj.bzhkcid
        where rc.scztw = '0' and rc.ksjhbh = #{ksjhbh} and rc.ccm = #{ccm}
        group by rc.ljkcbh
    </select>
    <select id="kshyqkcx" resultType="com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.KshyqkVO">
        SELECT
            SUM(1) AS ydrs,
            SUM(CASE WHEN rc.sfrc = '1' THEN 1 ELSE 0 END) AS hyrs
        FROM ks_ksrcxx rc
        WHERE rc.scztw = '0' AND rc.ksjhbh = #{ksjhbh} AND rc.ccm = #{ccm}
    </select>

    <select id="getSjsbTjxx" resultType="com.xcwlkj.identityverify.model.dos.SjsbTjxxDO">
        select count(*) as kcNum,
        sum(case when kcKsDone = kcKsTotal then 1 else 0 end) as kcDoneNum,
        sum(kcKsTotal) as ksTotalNum,
        sum(kcKsDone) as ksDoneNum,
        sum(kcFaceNum) as faceNum,
        sum(kcManualNum) as manualNum
        from (
        select kcbh,
        sum(case when sfrc in ('0', '1') then 1 else 0 end) as kcKsDone,
        count(*) as kcKsTotal,
        sum(case when sfrc in ('0', '1') and yzfs is not null then 1 else 0 end) as kcFaceNum,
        sum(case when sfrc in ('0', '1') and yzfs is null then 1 else 0 end) as kcManualNum
        from ks_ksrcxx
        where ksjhbh = #{ksjhbh}
        and ccm = #{ccm}
        and scztw = '0'
        group by  kcbh) rctj
    </select>
    <select id="ksrcZeroLb" resultType="com.xcwlkj.identityverify.model.vo.cxtj.ZeroKcItemVO">
        SELECT kcxx.csdm, kcxx.csmc, wg.ipdz, wg.firmwarebb wgbb, wg.xlh wgxlh,
           CASE WHEN wg.zxzt = '1' THEN '1' WHEN wg.zxzt = '0' OR (wg.zxzt IS NULL AND wg.sczt = '0') THEN '0' ELSE '-1' END sbzt,
           CASE WHEN zd.zxzt = '1' THEN '1' WHEN zd.zxzt = '0' OR (zd.zxzt IS NULL AND zd.sczt = '0') THEN '0' ELSE '-1' END ydzdzt,
           zd.firmwarebb ydzdbb, zd.xlh ydzdxlh, event.param4 errorType,event.event_desc_detail eventDetail
        FROM (
            SELECT rc.ksjhbh, rc.ccm, rc.bzhkdid kddm, kc.bzhkcid csdm, kc.bzhkcmc csmc, kc.kcbh FROM `ks_ksrcxx` rc
            LEFT JOIN `ks_kcxx` kc ON rc.ccm = kc.ccm AND rc.kcbh = kc.kcbh AND rc.bzhkdid = kc.bzhkdid AND rc.ksjhbh = kc.ksjhbh
            WHERE rc.scztw = "0" AND rc.ksjhbh = #{dto.ksjhbh} AND rc.ccm = #{dto.ccm}
            <if test="dto.csmc != null and dto.csmc != ''">
                AND kc.bzhkcmc LIKE CONCAT('%', CONCAT(#{dto.csmc}, '%'))
            </if>
            GROUP BY rc.ksjhbh, rc.ccm, rc.bzhkdid, kc.bzhkcid, kc.bzhkcmc, kc.kcbh
            HAVING
            <choose>
                <when test="dto.type == '2'.toString()">
                    SUM(CASE WHEN rc.sfrc = '9' THEN 1 ELSE 0 END) > 0
                </when>
                <otherwise>
                    SUM(CASE WHEN rc.sfrc = '9' THEN 1 ELSE 0 END) = COUNT(rc.ksrcbh)
                </otherwise>
            </choose>
        ) kcxx
        LEFT JOIN `ks_kssj_distribute_status` xf ON kcxx.ksjhbh = xf.ksjhbh AND kcxx.csdm = xf.bzhkcbh
        LEFT JOIN `sb_sbxx` zd ON xf.ydzd_download_sn = zd.xlh AND zd.sczt = "0"
        LEFT JOIN `sb_sbcsgx` gx ON kcxx.csdm = gx.csbh
        LEFT JOIN `sb_sbxx` wg ON gx.sbbh = wg.sbxxbh AND wg.sczt = "0" AND (wg.sblx = "kcwg" OR wg.sblx = "171")
        LEFT JOIN `ks_process_event` event ON kcxx.ksjhbh = event.ksjhbh AND event.ccm = kcxx.ccm AND kcxx.csdm = event.bzhkcid
--                                                   AND event.event_type = "-1401"
        <where>
            <if test="dto.errorType != null and dto.errorType != ''">
                AND event.param4 = #{dto.errorType}
            </if>
            <if test="dto.wgzt != null and dto.wgzt != ''">
                <choose>
                    <when test="dto.wgzt == '2'.toString()">
                        AND wg.sczt = '0'
                    </when>
                    <when test="dto.wgzt == '1'.toString()">
                        AND wg.sczt = '0' AND wg.zxzt = '1'
                    </when>
                    <when test="dto.wgzt == '0'.toString()">
                        AND wg.sczt = '0' AND (wg.zxzt = '0' OR wg.zxzt IS NULL)
                    </when>
                    <when test="dto.wgzt == '-1'.toString()">
                        AND wg.sczt != '0'
                    </when>
                </choose>
            </if>
            <if test="dto.ydzdzt != null and dto.ydzdzt != ''">
                <choose>
                    <when test="dto.ydzdzt == '2'.toString()">
                        AND zd.sczt = '0'
                    </when>
                    <when test="dto.ydzdzt == '1'.toString()">
                        AND zd.sczt = '0' AND zd.zxzt = '1'
                    </when>
                    <when test="dto.ydzdzt == '0'.toString()">
                        AND zd.sczt = '0' AND (zd.zxzt = '0' OR zd.zxzt IS NULL)
                    </when>
                    <when test="dto.ydzdzt == '-1'.toString()">
                        AND zd.sczt != '0'
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY kcxx.csdm
    </select>
    <select id="rcwbsjLb" resultType="com.xcwlkj.identityverify.model.vo.cxtj.RcwbsjItemVO">
        SELECT rc.kcmc, kc.bzhkcid, kc.bzhkcmc, rc.sbxlh, sb.zxzt sbzt, sb.firmwarebb ydzdbb, zs.sbkszs sbzs,
               sum(case when rc.sfrc in ('1', '0') then 1 else 0 end) rczs, date_format(zs.sbsj, '%Y-%m-%d %H:%i:%s') sbsj
        FROM `ks_ksrcxx` rc
        LEFT JOIN `ks_ydsb_sbkszs` zs ON rc.ksjhbh = zs.ksjhbh AND rc.ccm = zs.ccm AND rc.kcbh = zs.kcbh AND rc.ljkcbh = zs.ljkcbh AND rc.sbxlh = zs.sbxlh AND zs.sblx = 'bz'
        LEFT JOIN `sb_sbxx` sb ON rc.sbxlh = sb.xlh AND sb.sczt = "0"
        LEFT JOIN `ks_kcxx` kc ON rc.ksjhbh = kc.ksjhbh AND rc.ccm = kc.ccm AND rc.kcbh = kc.kcbh AND kc.sczt = '0'
        <where>
            rc.ksjhbh = #{dto.ksjhbh} and rc.ccm = #{dto.ccm}
            <if test="dto.sbzt != null and dto.sbzt != ''">
                and sb.zxzt = #{dto.sbzt}
            </if>
            and rc.sbxlh IS NOT null
        </where>
        GROUP BY rc.ksjhbh,rc.ccm,rc.bzhkcid,rc.kcbh,rc.kcmc,rc.ljkcbh,rc.sbxlh,sb.zxzt,sb.appbb,zs.sbkszs,zs.sbsj
        <if test="dto.errorType == '1'.toString()">
            having zs.sbkszs != sum(case when rc.sfrc in ('1', '0') then 1 else 0 end)
        </if>
    </select>
    <select id="cxljkcrcrs" resultType="com.xcwlkj.identityverify.model.dos.KsLjkcRcRsDO">
        SELECT max(rc.kcbh) kcbh, max(rc.kcmc) kcmc, max(rc.bzhkcid) wlkcbh, kcxx.bzhkcmc wlkcmc,
        max(rc.ljkcbh) ljkcbh, rc.sfrc sfrc, count(*) rs
        FROM ks_ksrcxx rc
        INNER JOIN
        (SELECT
        kc.ksjhbh ksjhbh, kc.ccm ccm, kc.kcbh kcbh, kc.bzhkcid bzhkcid,
        kc.bzhkcmc bzhkcmc, kc.ljkcbh ljkcbh
        FROM
        ks_kcxx kc
        <where>
            <if test="ksjhbh != null and ksjhbh != ''">
                AND kc.ksjhbh = #{ksjhbh}
            </if>
            <if test="ccm != null and ccm != ''">
                AND kc.ccm = #{ccm}
            </if>
        </where>
        ) kcxx ON rc.ksjhbh = kcxx.ksjhbh AND rc.ccm = kcxx.ccm AND rc.kcbh = kcxx.kcbh
        AND rc.ljkcbh = kcxx.ljkcbh
        WHERE 1=1
        <if test="ksjhbh != null and ksjhbh != ''">
            AND rc.ksjhbh = #{ksjhbh}
        </if>
        <if test="ccm != null and ccm != ''">
            AND rc.ccm = #{ccm}
        </if>
        <if test="bzhkcids != null and bzhkcids.size() > 0">
            AND rc.bzhkcid IN
            <foreach collection="bzhkcids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND rc.scztw = '0'
        GROUP BY rc.ljkcbh, rc.sfrc
        ORDER BY kcbh, kcmc, ljkcbh
    </select>


    <update id="batchUpdateTbzt">
        UPDATE ks_ksrcxx
        SET
        tbzt = CASE ksrcbh
        <foreach collection="ksrcxxList" item="item">
            WHEN #{item.ksrcbh} THEN #{item.tbzt}
        </foreach>
        END,
        sbsj = CASE ksrcbh
        <foreach collection="ksrcxxList" item="item">
            WHEN #{item.ksrcbh} THEN #{item.sbsj}
        </foreach>
        END
        WHERE ksrcbh IN (
        <foreach collection="ksrcxxList" item="item" separator=",">
            #{item.ksrcbh}
        </foreach>
        )
    </update>

    <update id="initByKsjhbh">
        UPDATE `ks_ksrcxx`
        SET update_time = NOW(),
            yzfs = null,
            yzjg = null,
            sfrc = "9",
            rcsj = null
        WHERE ksjhbh = #{ksjhbh}
    </select>

    <!-- 根据考试计划编号和场次码查询考生入场信息用于上传到上级平台 -->
    <select id="selectForUploadToProvince" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM ks_ksrcxx
        WHERE ksjhbh = #{ksjhbh}
          AND ccm = #{ccm}
          AND scztw = '0'
          AND sfrc = '1'
        ORDER BY ksrcbh
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计符合条件的考生入场信息总数 -->
    <select id="countForUploadToProvince" resultType="int">
        SELECT COUNT(1)
        FROM ks_ksrcxx
        WHERE ksjhbh = #{ksjhbh}
          AND ccm = #{ccm}
          AND scztw = '0'
          AND sfrc = '1'
    </update>

</mapper>
