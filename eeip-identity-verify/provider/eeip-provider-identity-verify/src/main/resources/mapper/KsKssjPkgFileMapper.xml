<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.identityverify.mapper.KsKssjPkgFileMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.identityverify.model.domain.KsKssjPkgFile">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="ksjhbh" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="pkg_catalog" jdbcType="CHAR" property="pkgCatalog" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="file_md" jdbcType="VARCHAR" property="fileMd" />
        <result column="file_path" jdbcType="VARCHAR" property="filePath" />
        <result column="pkg_type" jdbcType="VARCHAR" property="pkgType" />
        <result column="bzhkdid" jdbcType="VARCHAR" property="bzhkdid" />
        <result column="bzhkdmc" jdbcType="VARCHAR" property="bzhkdmc" />
        <result column="kdmc" jdbcType="VARCHAR" property="kdmc" />
        <result column="dfs_file_obj_uri" jdbcType="VARCHAR" property="dfsFileObjUri" />
        <result column="dfs_file_path_uri" jdbcType="VARCHAR" property="dfsFilePathUri" />
        <result column="version" jdbcType="VARCHAR" property="version" />
        <result column="is_download" jdbcType="VARCHAR" property="isDownload" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="bzhkcbh" jdbcType="VARCHAR" property="bzhkcbh" />
        <result column="bzhkcmc" jdbcType="VARCHAR" property="bzhkcmc" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        ksjhbh,
        pkg_catalog,
        file_name,
        file_md,
        file_path,
        pkg_type,
        bzhkdid,
        bzhkdmc,
        kdmc,
        dfs_file_obj_uri,
        dfs_file_path_uri,
        version,
        is_download,
        sczt,
        create_time,
        update_time,
        bzhkcbh,
        bzhkcmc

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND ksjhbh = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="pkgCatalog != null and pkgCatalog != ''">
            AND pkg_catalog = #{pkgCatalog,jdbcType=CHAR}
        </if>
        <if test="fileName != null and fileName != ''">
            AND file_name = #{fileName,jdbcType=VARCHAR}
        </if>
        <if test="fileMd != null and fileMd != ''">
            AND file_md = #{fileMd,jdbcType=VARCHAR}
        </if>
        <if test="filePath != null and filePath != ''">
            AND file_path = #{filePath,jdbcType=VARCHAR}
        </if>
        <if test="pkgType != null and pkgType != ''">
            AND pkg_type = #{pkgType,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdid != null and bzhkdid != ''">
            AND bzhkdid = #{bzhkdid,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdmc != null and bzhkdmc != ''">
            AND bzhkdmc = #{bzhkdmc,jdbcType=VARCHAR}
        </if>
        <if test="kdmc != null and kdmc != ''">
            AND kdmc = #{kdmc,jdbcType=VARCHAR}
        </if>
        <if test="dfsFileObjUri != null and dfsFileObjUri != ''">
            AND dfs_file_obj_uri = #{dfsFileObjUri,jdbcType=VARCHAR}
        </if>
        <if test="dfsFilePathUri != null and dfsFilePathUri != ''">
            AND dfs_file_path_uri = #{dfsFilePathUri,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=VARCHAR}
        </if>
        <if test="isDownload != null and isDownload != ''">
            AND is_download = #{isDownload,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="bzhkcbh != null and bzhkcbh != ''">
            AND bzhkcbh = #{bzhkcbh,jdbcType=VARCHAR}
        </if>
        <if test="bzhkcmc != null and bzhkcmc != ''">
            AND bzhkcmc = #{bzhkcmc,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="ksjhbh != null ">
            ksjhbh = #{ksjhbh,jdbcType=VARCHAR},
        </if>
        <if test="pkgCatalog != null ">
            pkg_catalog = #{pkgCatalog,jdbcType=CHAR},
        </if>
        <if test="fileName != null ">
            file_name = #{fileName,jdbcType=VARCHAR},
        </if>
        <if test="fileMd != null ">
            file_md = #{fileMd,jdbcType=VARCHAR},
        </if>
        <if test="filePath != null ">
            file_path = #{filePath,jdbcType=VARCHAR},
        </if>
        <if test="pkgType != null ">
            pkg_type = #{pkgType,jdbcType=VARCHAR},
        </if>
        <if test="bzhkdid != null ">
            bzhkdid = #{bzhkdid,jdbcType=VARCHAR},
        </if>
        <if test="bzhkdmc != null ">
            bzhkdmc = #{bzhkdmc,jdbcType=VARCHAR},
        </if>
        <if test="kdmc != null ">
            kdmc = #{kdmc,jdbcType=VARCHAR},
        </if>
        <if test="dfsFileObjUri != null ">
            dfs_file_obj_uri = #{dfsFileObjUri,jdbcType=VARCHAR},
        </if>
        <if test="dfsFilePathUri != null ">
            dfs_file_path_uri = #{dfsFilePathUri,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=VARCHAR},
        </if>
        <if test="isDownload != null ">
            is_download = #{isDownload,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="bzhkcbh != null ">
            bzhkcbh = #{bzhkcbh,jdbcType=VARCHAR},
        </if>
        <if test="bzhkcmc != null ">
            bzhkcmc = #{bzhkcmc,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

</mapper>
