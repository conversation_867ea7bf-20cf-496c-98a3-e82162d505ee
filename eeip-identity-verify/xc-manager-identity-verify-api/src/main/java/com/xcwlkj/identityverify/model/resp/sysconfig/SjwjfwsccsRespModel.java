package com.xcwlkj.identityverify.model.resp.sysconfig;

import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 上级文件服务上传测试响应
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SjwjfwsccsRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 测试状态：0-失败，1-成功 */
    private String status;
    
    /** 测试结果消息 */
    private String message;
    
    /** 图片上传结果详情 */
    private String imageUploadDetail;
    
    /** 文本上传结果详情 */
    private String textUploadDetail;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}