/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.req.jxhy;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 教学数据下发任务日志下载请求
 * <AUTHOR>
 * @version $Id: JxsjxfrwrzxzReqModel.java, v 0.1 2024年07月17日 16时30分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JxsjxfrwrzxzReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 任务id */
    @NotBlank(message = "任务id不能为空")
    private String id;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}