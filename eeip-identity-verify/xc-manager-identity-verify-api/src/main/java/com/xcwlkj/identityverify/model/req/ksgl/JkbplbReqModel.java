/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.req.ksgl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 监考编排列表请求
 * <AUTHOR>
 * @version $Id: JkbplbReqModel.java, v 0.1 2023年10月13日 15时21分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JkbplbReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    private String ccm;
    /** 身份证件号码 */
    private String sfzjhm;
    /** 姓名 */
    private String xm;
    /** 岗位类型码 */
    private String gwlxm;
    /** 排序列 */
    private String orderCol;
    /** 排序方式 asc|desc */
    private String orderType;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}