/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.sysconfig;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 上级平台（省平台）信息配置dto
 * <AUTHOR>
 * @version $Id: SjptxxpzDTO.java, v 0.1 2023年11月20日 11时31分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SjptxxpzDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 上级平台服务地址 */
    private String url;
    /** 上级平台账户用户名 */
    private String username;
    /** 上级平台账户密码 先明文传 */
    private String password;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
