package com.xcwlkj.identityverify.model.req.sysconfig;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 上级文件服务上传测试请求
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SjwjfwsccsReqModel extends RemoteReqBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}