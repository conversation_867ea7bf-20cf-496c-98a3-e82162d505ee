package com.xcwlkj.identityverify.model.dto.sysconfig;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 上级文件服务上传测试DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SjwjfwsccsDTO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}