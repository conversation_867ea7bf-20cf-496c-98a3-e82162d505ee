/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.sysconfig;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 平台配置信息详情响应
 * <AUTHOR>
 * @version $Id: PtpzxxxqRespModel.java, v 0.1 2023年12月22日 16时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PtpzxxxqRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /**  */
    private String id;
    /** 平台类型 */
    private String platType;
    /** 服务id */
    private String serverIp;
    /** 服务端口 */
    private String serverPort;
    /** 代理ip */
    private String proxyIp;
    /** 代理端口 */
    private String proxyPort;
    /**  */
    private String appId;
    /**  */
    private String appKey;
    /** 当前默认平台 */
    private String defaultPlat;
    /** 文件服务器地址 */
    private String fileServerUrl;
    /** 文件服务渠道 */
    private String fileServerChannel;
    /** 文档服务 */
    private String documentService;
    /** 文件服务appId */
    private String fileServiceAppId;
    /** 文件服务appSecret */
    private String fileServiceAppSecret;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}