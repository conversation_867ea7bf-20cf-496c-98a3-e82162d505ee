/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.schedulejob;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 定时任务详情响应
 * <AUTHOR>
 * @version $Id: JobDetailRespModel.java, v 0.1 2023年10月12日 17时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JobDetailRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** bean名称 */
    private String beanName;
    /** 参数 */
    private String params;
    /** cron表达式不能为空 */
    private String cronExpression;
    /** 备注 */
    private String remark;
    /** 任务ID */
    private String jobId;
    /** 1-正常 0-停止 */
    private Integer status;
    /** 任务名称 */
    private String jobName;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}