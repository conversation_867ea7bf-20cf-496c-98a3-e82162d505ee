/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.resp.sysconfig;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 获取本校配置信息响应
 * <AUTHOR>
 * @version $Id: HqbxpzxxRespModel.java, v 0.1 2023年11月20日 13时52分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HqbxpzxxRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 学校名称 */
    private String xxmc;
    /** 组织机构码 */
    private String zzjgm;
    /** 学校地址 */
    private String xxdz;
    /** 学校所在行政区划码 */
    private String xzqhm;
    /** 设备序列号 */
    private String sbxlh;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}