/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.vo.cxtj;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


/**
 * 考场入场未报数据列表vo
 * <AUTHOR>
 * @version $Id: RcwbsjLbVO.java, v 0.1 2024年08月21日 15时23分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class RcwbsjLbVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 入场未报数据列表 */
    private List<RcwbsjItemVO> rcwbsjList;
    /** 总条数 */
    private Integer totalRows;

}
