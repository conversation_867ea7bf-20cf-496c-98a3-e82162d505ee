/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.dto.sysconfig;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 平台配置信息添加/编辑dto
 * <AUTHOR>
 * @version $Id: PtpzxxtjDTO.java, v 0.1 2023年12月22日 15时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class PtpzxxtjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 平台类型 */
    @NotBlank(message = "平台类型不能为空")
    private String platType;
    /** 服务ip */
    @NotBlank(message = "服务ip不能为空")
    private String serverIp;
    /** 服务端口 */
    @NotBlank(message = "服务端口不能为空")
    private String serverPort;
    /** 代理IP */
    @NotBlank(message = "代理IP不能为空")
    private String proxyIp;
    /** 代理端口 */
    @NotBlank(message = "代理端口不能为空")
    private String proxyPort;
    /**  */
    @NotBlank(message = "不能为空")
    private String appId;
    /**  */
    @NotBlank(message = "不能为空")
    private String appKey;
    /** 传id时为编辑，不传为空为添加 */
    private String id;
    /** 文件服务器地址 */
    private String fileServerUrl;
    /** 文件服务渠道 */
    private String fileServerChannel;
    /** 文档服务 */
    private String documentService;
    /** 文件服务appId */
    private String fileServiceAppId;
    /** 文件服务appSecret */
    private String fileServiceAppSecret;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
