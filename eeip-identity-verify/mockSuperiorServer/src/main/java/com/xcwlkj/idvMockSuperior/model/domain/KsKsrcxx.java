/**
 * xcwlkj.com Inc.
 * Copyright (c) 2024-2034 All Rights Reserved.
 */
package com.xcwlkj.idvMockSuperior.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 考生入场信息
 * 
 * <AUTHOR>
 * @version $Id: KsKsrcxx.java, v 0.1 2024年02月22日 14时55分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_ksrcxx")
public class KsKsrcxx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 入场信息唯一编号 */
    @Id
    @Column(name = "ksrcbh")
    private String            ksrcbh;
    /** 考试计划编号 */
    @Column(name = "ksjhbh")
    private String            ksjhbh;
    /** 场次码 */
    @Column(name = "ccm")
    private String            ccm;
    /** 准考证号 */
    @Column(name = "zkzh")
    private String            zkzh;
    /** 验证方式 */
    @Column(name = "yzfs")
    private String            yzfs;
    /** 验证结果 */
    @Column(name = "yzjg")
    private String            yzjg;
    /** 是否入场 */
    @Column(name = "sfrc")
    private String            sfrc;
    /** 入场时间 */
    @Column(name = "rcsj")
    private String            rcsj;
    /** 标准化考点标识号 */
    @Column(name = "bzhkdid")
    private String            bzhkdid;
    /** 标准化考场标志号 */
    @Column(name = "bzhkcid")
    private String            bzhkcid;
    /** 删除状态位 */
    @Column(name = "scztw")
    private String            scztw;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 上报状态   0-不上报    1-要上报（应上报） */
    @Column(name = "sbzt")
    private String            sbzt;
    /** 最近一次上报的时间 */
    @Column(name = "sbsj")
    private Date            sbsj;
    /** 同步状态 */
    @Column(name = "tbzt")
    private String            tbzt;
    /** 更新状态 */
    @Column(name = "gxzt")
    private String            gxzt;
    /** 考点编号 */
    @Column(name = "kdbh")
    private String            kdbh;
    /** 考点名称 */
    @Column(name = "kdmc")
    private String            kdmc;
    /** 考场编号 */
    @Column(name = "kcbh")
    private String            kcbh;
    /** 考场名称 */
    @Column(name = "kcmc")
    private String            kcmc;
    /** 考生姓名 */
    @Column(name = "ksxm")
    private String            ksxm;
    /** 座位号 */
    @Column(name = "zwh")
    private String            zwh;
    /** 座位号_排序 */
    @Column(name = "zwh_px")
    private Integer            zwhPx;
    /** 考试类型码 */
    @Column(name = "kslxm")
    private String            kslxm;
    /** 考试类型名称 */
    @Column(name = "kslxmc")
    private String            kslxmc;
    /** 科目名称 */
    @Column(name = "kmmc")
    private String            kmmc;
    /** 科目代码 */
    @Column(name = "kmdm")
    private String            kmdm;
    /** 身份证号 */
    @Column(name = "sfzh")
    private String            sfzh;
    /** 班级 */
    @Column(name = "bj")
    private String            bj;
    /** 报名点代码 */
    @Column(name = "bmddm")
    private String            bmddm;
    /** 入场数据来源  web - 来自于 平台的页面 ；pad  - 来自于 核验终端 */
    @Column(name = "rcsjly")
    private String            rcsjly;
    /** 入场时间分组 */
    @Column(name = "rcsjfz")
    private String            rcsjfz;
    /** 考生号 */
    @Column(name = "ksh")
    private String            ksh;
    /** 人工验证结果 1-通过 0-不通过 */
    @Column(name = "rgyzjg")
    private String            rgyzjg;
    /** 考场类型代码 */
    @Column(name = "cslxdm")
    private String            cslxdm;
    /** 科类代码 */
    @Column(name = "bkkl")
    private String            bkkl;
    /** 逻辑考场编号 */
    @Column(name = "ljkcbh")
    private String            ljkcbh;
    /** 入场人脸照片 */
    @Column(name = "rcrlzp")
    private String            rcrlzp;
    /** 身份证照片 */
    @Column(name = "sfzp")
    private String            sfzp;
    /** 设备序列号 */
    @Column(name = "sbxlh")
    private String            sbxlh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


