/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.idvMockSuperior.exception;

import com.xcwlkj.base.exception.ApplicationBusinessException;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @version $Id: GlobalExceptionHandler.java, v 0.1 2018年11月20日 下午12:45:13 danfeng.zhou Exp $
 */
@ControllerAdvice
@Slf4j
@ConditionalOnProperty(name="spring.application.name", havingValue = "idvMockSuperior-service")
public class GlobalExceptionHandler {

    /**
     * 业务异常
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public Wrapper<String> customHandler(BusinessException e, HttpServletResponse response) throws IOException {
        return WrapMapper.wrap(e.getCode(), e.getMessage());
    }
    
    /**
     * 业务异常
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler(ApplicationBusinessException.class)
    @ResponseBody
    public Wrapper<String> applicationHandler(BusinessException e, HttpServletResponse response) throws IOException {
        log.info("GlobalExceptionHandler 拦截到【应用业务异常】，异常信息："+e.getMessage(),e);
        return WrapMapper.wrap(e.getCode(), e.getMessage());
    }

    /**
     * 系统异常处理
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Wrapper<String> exceptionHandler(Exception e) {
        log.warn("GlobalExceptionHandler 拦截到Exception异常:", e);
        return WrapMapper.error(e.getCause().getMessage());
    }

    /**
     * 
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler({ BindException.class })
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Wrapper<String> validateErrorHandler(BindException e) throws IOException {
        return buildBindingResultWrapper(e.getBindingResult());
    }

    /**
     * 
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler({ MethodArgumentNotValidException.class })
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Wrapper<String> validateErrorHandler(MethodArgumentNotValidException e) throws IOException {
        return buildBindingResultWrapper(e.getBindingResult());
    }

    @ExceptionHandler(ServletRequestBindingException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Wrapper<String> bindErrorHandler(ServletRequestBindingException e) {
        log.warn("GlobalExceptionHandler 拦截到ServletRequestBindingException异常:", e);
        return WrapMapper.error(e.getMessage());

    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Wrapper<String> bindErrorHandler(MissingServletRequestParameterException e) {
        return WrapMapper.illegalArgument(e.getParameterName());
    }

    /**
     * 
     * @param bindingResult
     * @return
     */
    private Wrapper<String> buildBindingResultWrapper(BindingResult bindingResult) {
        List<FieldError> errorList = bindingResult.getFieldErrors();
        String str = MessageFormat.format("请求报文[{0}]参数有误", errorList.get(0).getField());
        if (StringUtils.isNotBlank(errorList.get(0).getDefaultMessage())) {
            str = str + ",cause by:" + errorList.get(0).getDefaultMessage();
        }
        return WrapMapper.illegalArgument(str);
    }
}
