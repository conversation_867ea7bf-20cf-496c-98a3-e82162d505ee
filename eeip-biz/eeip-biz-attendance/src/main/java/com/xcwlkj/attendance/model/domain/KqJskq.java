/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 教师考勤表
 * 
 * <AUTHOR>
 * @version $Id: KqJskq.java, v 0.1 2023年07月17日 13时32分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "kq_jskq")
public class KqJskq implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "bh")
    private Integer            bh;
    /**  */
    @Column(name = "pkbbh")
    private String            pkbbh;
    /** 排课开始时间 */
    @Column(name = "pkkssj")
    private Date            pkkssj;
    /** 排课结束时间 */
    @Column(name = "pkjssj")
    private Date            pkjssj;
    /**  */
    @Column(name = "gh")
    private String            gh;
    /** 考勤结果
            0：正常
            1：请假
            2：迟到
            3：早退
            4：旷课 */
    @Column(name = "kqjg")
    private Integer            kqjg;
    /** 结果描述 */
    @Column(name = "jgms")
    private String            jgms;
    /**  */
    @Column(name = "kqsj")
    private Date            kqsj;
    /** 创建时间 */
    @Column(name = "cjsj")
    private Date            cjsj;
    /** 更新时间 */
    @Column(name = "gxsj")
    private Date            gxsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


