/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.biz.core.model.InfoPubBodyParams;
import com.xcwlkj.biz.core.model.enums.InfoPubActionType;
import com.xcwlkj.biz.core.service.BoardOperateService;
import com.xcwlkj.biz.core.util.SbxxUtils;
import com.xcwlkj.campus.cache.ZrDateRedisCache;
import com.xcwlkj.campus.mapper.ZhxyBjzrMapper;
import com.xcwlkj.campus.model.domain.ZhxyBjzr;
import com.xcwlkj.campus.model.domain.ZrDateDO;
import com.xcwlkj.campus.model.dto.classduty.AddDTO;
import com.xcwlkj.campus.model.dto.classduty.BatchDeleteDTO;
import com.xcwlkj.campus.model.dto.classduty.DistributeDTO;
import com.xcwlkj.campus.model.dto.classduty.EditDTO;
import com.xcwlkj.campus.model.dto.classduty.RemoteWeekTableDTO;
import com.xcwlkj.campus.model.dto.classduty.TodayDutyStuListDTO;
import com.xcwlkj.campus.model.dto.classduty.WeekTableDTO;
import com.xcwlkj.campus.model.enums.PublishBizType;
import com.xcwlkj.campus.model.enums.RepeatType;
import com.xcwlkj.campus.model.enums.SfdszEnum;
import com.xcwlkj.campus.model.vo.classduty.AddVO;
import com.xcwlkj.campus.model.vo.classduty.RemoteWeekTableVO;
import com.xcwlkj.campus.model.vo.classduty.RzrbItemVO;
import com.xcwlkj.campus.model.vo.classduty.TodayDutyStuListVO;
import com.xcwlkj.campus.model.vo.classduty.WeekTableVO;
import com.xcwlkj.campus.model.vo.classduty.ZrsItemVO;
import com.xcwlkj.campus.model.vo.classduty.ZzrbItemVO;
import com.xcwlkj.campus.service.ZhxyBjzrService;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.resourcecenter.model.dto.dictdata.GetDataInfoDTO;
import com.xcwlkj.resourcecenter.model.vo.dictdata.DictDataInfoItemVO;
import com.xcwlkj.resourcecenter.model.vo.dictdata.GetDataInfoVO;
import com.xcwlkj.resourcecenter.service.DictdataFeignApi;
import com.xcwlkj.sturegister.model.dto.bj.GetBjDetailsByJshDTO;
import com.xcwlkj.sturegister.model.dto.bj.GetManagedClassDTO;
import com.xcwlkj.sturegister.model.dto.quarter.GetXnXqByDateDTO;
import com.xcwlkj.sturegister.model.dto.schoolrollinfo.GetInfoDTO;
import com.xcwlkj.sturegister.model.dto.stuinfo.SelectByXhListDTO;
import com.xcwlkj.sturegister.model.vo.bj.BjxxItemVO;
import com.xcwlkj.sturegister.model.vo.bj.GetBjDetailsByJshVO;
import com.xcwlkj.sturegister.model.vo.bj.GetManagedClassVO;
import com.xcwlkj.sturegister.model.vo.quarter.GetXnXqByDateVO;
import com.xcwlkj.sturegister.model.vo.quarter.XnXqItemVO;
import com.xcwlkj.sturegister.model.vo.schoolrollinfo.GetInfoVO;
import com.xcwlkj.sturegister.model.vo.schoolrollinfo.SchoolRollSysItemVO;
import com.xcwlkj.sturegister.model.vo.stuinfo.SelectByXhListItemVO;
import com.xcwlkj.sturegister.model.vo.stuinfo.SelectByXhListVO;
import com.xcwlkj.sturegister.service.BjFeignApi;
import com.xcwlkj.sturegister.service.QuarterFeignApi;
import com.xcwlkj.sturegister.service.SchoolrollinfoFeignApi;
import com.xcwlkj.sturegister.service.StuInfoFeignApi;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.RequestUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 班级值日表服务
 * <AUTHOR>
 * @version $Id: ZhxyBjzrServiceImpl.java, v 0.1 2025年06月19日 13时10分 xcwlkj.com Exp $
 */
@Service("zhxyBjzrService")
public class ZhxyBjzrServiceImpl  implements ZhxyBjzrService  {

    @Resource
    private ZhxyBjzrMapper modelMapper;

    @Resource
    private BjFeignApi bjFeignApi;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SbxxUtils sbxxUtils;

    @Resource(name = "campusBoardOperateService")
    private BoardOperateService boardOperateService;

    @Resource
    private QuarterFeignApi quarterFeignApi;

    @Resource
    private DictdataFeignApi dictdataFeignApi;

    @Resource
    private StuInfoFeignApi stuInfoFeignApi;

    @Resource
    private SchoolrollinfoFeignApi schoolrollinfoFeignApi;

    @Resource
    private ZrDateRedisCache zrDateRedisCache;



    @Transactional
    @Override
    public AddVO add(AddDTO dto) {
        ZhxyBjzr zhxyBjzr = new ZhxyBjzr();

        BeanUtils.copyProperties(dto,zhxyBjzr);

        //起始周、终止周
        zhxyBjzr.setQsz(dto.getKszc());
        zhxyBjzr.setZzz(dto.getJszc());

        List<String> bhList = new ArrayList<>();
        // 指定所在周次
        if (dto.getZrszzs() != null) {
            String bh = IdGenerateUtil.generateId();
            zhxyBjzr.setBh(bh);//编号
            insertSelective(zhxyBjzr);
            bhList.add(bh);
        } else { // 不指定则每周插入一条
            for (int i = dto.getKszc(); i <= dto.getJszc(); i++) {
                if ((StringUtil.equals(dto.getSfdsz(), SfdszEnum.ODD_WEEK.getCode()) && i % 2 == 1)
                        || (StringUtil.equals(dto.getSfdsz(), SfdszEnum.DOUBLE_WEEK.getCode()) && i % 2 == 0)
                        || StringUtil.equals(dto.getSfdsz(), SfdszEnum.ALL.getCode())) {
                    String bh = IdGenerateUtil.generateId();
                    zhxyBjzr.setBh(bh);//编号
                    zhxyBjzr.setZrszzs(i);
                    insertSelective(zhxyBjzr);
                    bhList.add(bh);
                }
            }
        }

        AddVO addVO = new AddVO();
        addVO.setBhList(bhList);
        return addVO;
    }

    @Transactional
    @Override
    public void edit(EditDTO dto) {
        ZhxyBjzr zhxyBjzr = new ZhxyBjzr();

        String userName = RequestUtil.getLoginUser().getUserName();
        String roleId = RequestUtil.getLoginUser().getCurrRoleId();

        if (StringUtil.equals("002", roleId)) {
            List<String> managedClass = new ArrayList<>();
            GetManagedClassDTO getManagedClassDTO = new GetManagedClassDTO();
            getManagedClassDTO.setBzrgh(userName);
            Wrapper<GetManagedClassVO> managedClassWrapper = bjFeignApi.getManagedClass(getManagedClassDTO);
            if (managedClassWrapper != null && managedClassWrapper.getResult() != null && managedClassWrapper.getResult().getBjxxList() != null) {
                // 获取管理班级的班级号列表
                managedClass = managedClassWrapper.getResult().getBjxxList().stream()
                        .map(BjxxItemVO::getBjh)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(managedClass)) {
                    List<String> jxbList = Arrays.asList(dto.getZrbbh().split(","));
                    List<String> intersection = jxbList.stream()
                            .filter(managedClass::contains)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(intersection)) {
                        throw new BusinessException("无法修改非管理班级");
                    }
                    dto.setZrbbh(String.join(",", intersection));
                }
            }
            if (CollectionUtils.isEmpty(managedClass)) {
                throw new BusinessException("无法修改非管理班级");
            }
        }
        BeanUtil.copyProperties(dto, zhxyBjzr);

        zhxyBjzr.setQsz(dto.getKszc()); //开始周
        zhxyBjzr.setZzz(dto.getJszc()); //结束周
        zhxyBjzr.setXgsj(new Date());//修改时间
        modelMapper.updateByPrimaryKeySelective(zhxyBjzr);
    }

    @Override
    public WeekTableVO weekTable(WeekTableDTO dto) {
        WeekTableVO result = new WeekTableVO();
        Example example = new Example(ZhxyBjzr.class);
        Example.Criteria cr = example.createCriteria();
        if (StringUtil.isNotBlank(dto.getKkxnd())) {
            cr.andEqualTo("kkxnd", dto.getKkxnd());
        }
        if (StringUtil.isNotBlank(dto.getKkxqm())) {
            cr.andEqualTo("kkxqm", dto.getKkxqm());
        }
        if (StringUtil.isNotBlank(dto.getBah())) {
            cr.andEqualTo("zrbbh", dto.getBah());
        }
        // 周次
        cr.andEqualTo("zrszzs", dto.getCxzc());
        List<ZhxyBjzr> list = modelMapper.selectByExample(example);
        PageInfo<ZhxyBjzr> pageInfo = new PageInfo<ZhxyBjzr>(list);
        result.setTotalRows((int) pageInfo.getTotal());


        Map<Integer, ZzrbItemVO> zzrbItemVOMap = new LinkedHashMap<>();
        for (int i = 1; i < 8; i++) {
            ZzrbItemVO zzrbItemVO = new ZzrbItemVO();
            zzrbItemVO.setXq(i);
            zzrbItemVO.setRzrb(new ArrayList<>());
            zzrbItemVOMap.put(i, zzrbItemVO);
        }
        for (ZhxyBjzr zhxyBjzr : list) {
            RzrbItemVO itemVO = new RzrbItemVO();
            BeanUtil.copyProperties(zhxyBjzr, itemVO);

            itemVO.setKszc(zhxyBjzr.getQsz());//开始周次
            itemVO.setJszc(zhxyBjzr.getZzz());//结束周次

            zzrbItemVOMap.get(itemVO.getXq()).getRzrb().add(itemVO);
        }

        result.setZzrbList(new ArrayList<>(zzrbItemVOMap.values()));
        return result;
    }

    @Override
    public void batchDelete(BatchDeleteDTO dto) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                Example emZrb = new Example(ZhxyBjzr.class);
                emZrb.createCriteria()
                        .andIn("bh", dto.getBhList());
                modelMapper.deleteByExample(emZrb);
            }
        });
    }

    @Transactional
    @Override
    public void distribute(DistributeDTO dto) {
        XcSsoUser loginUser = RequestUtil.getLoginUser();
        modelMapper.publishByJshList(dto,loginUser);

        List<List<ZhxyBjzr>> list = new ArrayList<>();
        for (String jsh : dto.getJshList()){
            Example example = new Example(ZhxyBjzr.class);
            Example.Criteria cr = example.createCriteria();
            if (StringUtil.isNotBlank(dto.getKkxnd())) {
                cr.andEqualTo("kkxnd", dto.getKkxnd());
            }
            if (StringUtil.isNotBlank(dto.getKkxqm())) {
                cr.andEqualTo("kkxqm", dto.getKkxqm());
            }
            if (StringUtil.isNotBlank(jsh)) {
                cr.andEqualTo("jsh", jsh);
            }
            // 周次
            cr.andEqualTo("zrszzs", dto.getCxzc());
            List<ZhxyBjzr> itemList = modelMapper.selectByExample(example);
            list.add(itemList);
        }

        // 组织入参
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list",list);
        InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.pub.getValue(),
                PublishBizType.CLASS_DUTY.getCode(), IdGenerateUtil.generateId(), jsonObject);
        boardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(dto.getJshList(),"4869"), pubBodyParams);
    }

    @Transactional
    @Override
    public void importDuty(MultipartFile file,String zrbbh,String zrbmc,String jsh,String jsmc) throws IOException, ParseException {
        File convertedFile = new File(file.getOriginalFilename());
        FileOutputStream fos = new FileOutputStream(convertedFile);
        fos.write(file.getBytes());
        fos.close();
        FileInputStream fis = new FileInputStream(convertedFile);
        // 解析文件数据
        Sheet sheet = new XSSFWorkbook(fis).getSheetAt(0);
        for(int i = 2;i <= sheet.getLastRowNum();i++){
            Row row = sheet.getRow(i);

            // 获取值日项属性码、值日项名称
            DictDataInfoItemVO dictDataInfoItemVO = getZrx(row.getCell(0).getStringCellValue(), i+1);
            String zrxsxm = dictDataInfoItemVO.getDictValue();
            String zrxmc = dictDataInfoItemVO.getDictLabel();

            // 获取重复周期
            String cflx = "";
            String sfdsz = "";
            Cell cflxCell = row.getCell(10);
            if (cflxCell == null){
                // 若重复类型不设置，则默认每周重复添加
                sfdsz = RepeatType.every.getCode();
            } else {
                cflx = cflxCell.getStringCellValue();
                sfdsz = RepeatType.getDesc(cflx).getCode();
            }

            // 获取当前日期所在周周一
            LocalDate monLocalDate = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            // 获取本周日的日期
            LocalDate sunLocalDate = LocalDate.now().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            // 若时间段不配置，则默认只导入当前周的值日信息
            Cell ksrqCell = row.getCell(8);
            Cell jsrqCell = row.getCell(9);
            Date ksrq = new Date();
            Date jsrq = new Date();
            if (ksrqCell == null){
                ksrq = Date.from(monLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } else {
                ksrq = row.getCell(8).getDateCellValue();
            }

            if (jsrqCell == null){
                jsrq = Date.from(sunLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } else {
                jsrq = row.getCell(9).getDateCellValue();
            }
            // 获取学年学期信息(值日导入不作用于已经过去的周)
            Date nowRq = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
            if (nowRq.after(ksrq)){
                // 当前日期所在周周一
                ksrq = Date.from(monLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
            List<XnXqItemVO> xnxqList = getXnxq(ksrq, jsrq, i+1);

            for (XnXqItemVO xnxq : xnxqList){
                for (int xq = 1 ; xq <= 7; xq++){
                    // 获取值日生学号
                    Cell xhCell = row.getCell(xq);
                    if (xhCell == null){
                        continue;
                    }
                    String zrsxh = row.getCell(xq).getStringCellValue();
                    if (StringUtil.isBlank(zrsxh)){
                        continue;
                    }
                    List<String> zrsxhList = getZrsxh(zrsxh, i+1, xq, zrbbh);

                    // 获取开始/结束周次
                    Integer kszc = getKszc(ksrq, xnxq, xq);
                    Integer jszc = getJszc(jsrq, xnxq, xq);

                    // 删除指定值日数据
                    Example zrExample = new Example(ZhxyBjzr.class);
                    Example.Criteria zrcr = zrExample.createCriteria();
                    zrcr.andEqualTo("kkxnd", xnxq.getXn());
                    zrcr.andEqualTo("kkxqm", xnxq.getXqm());
                    zrcr.andLessThanOrEqualTo("qsz",kszc);
                    zrcr.andGreaterThanOrEqualTo("zzz",jszc);
                    zrcr.andEqualTo("xq",xq);
                    zrcr.andEqualTo("zrbbh",zrbbh);
                    zrcr.andEqualTo("zrxsxm",zrxsxm);
                    modelMapper.deleteByExample(zrExample);

                    // 组装新增值日参数
                    AddDTO addDTO = new AddDTO();
                    addDTO.setKkxnd(xnxq.getXn());
                    addDTO.setKkxqm(xnxq.getXqm());
                    addDTO.setXq(xq);
                    // 获取开始周次和结束周次
                    addDTO.setKszc(kszc);
                    addDTO.setJszc(jszc);
                    addDTO.setSfdsz(sfdsz);
                    addDTO.setZrbbh(zrbbh);
                    addDTO.setZrbmc(zrbmc);
                    addDTO.setZrxsxm(zrxsxm);
                    addDTO.setZrxmc(zrxmc);
                    // 获取值日生姓名
                    String zrsxm = getZrsxm(zrsxhList, i+1);
                    addDTO.setZrsxh(zrsxh);
                    addDTO.setZrsxm(zrsxm.substring(0,zrsxm.lastIndexOf(",")));
                    addDTO.setJsh(jsh);
                    addDTO.setZrdd(jsmc);
                    // 导入数据
                    add(addDTO);
                }
            }
        }
        fis.close();
    }

    @Override
    public RemoteWeekTableVO remoteWeekTable(RemoteWeekTableDTO dto) {
        RemoteWeekTableVO result = new RemoteWeekTableVO();
        WeekTableDTO weekTableDTO = new WeekTableDTO();
        BeanUtils.copyProperties(dto,weekTableDTO);

        // 根据教室号查询对应bah
        GetBjDetailsByJshDTO getBjDetailsByJshDTO = new GetBjDetailsByJshDTO();
        getBjDetailsByJshDTO.setJsh(dto.getJsh());
        Wrapper<GetBjDetailsByJshVO> getBjDetailsByJshVOWrapper = bjFeignApi.getBjDetailsByJsh(getBjDetailsByJshDTO);
        if (getBjDetailsByJshVOWrapper.getCode() != 200) {
            throw new BusinessException("bjFeignApi.getBjDetailsByJsh()接口方法调用异常");
        }
        List<String> bahList = getBjDetailsByJshVOWrapper.getResult().getBahList();
        if (CollectionUtils.isEmpty(bahList)){
            throw new BusinessException("该教室号无绑定班级");
        }

        weekTableDTO.setBah(bahList.get(0));
        WeekTableVO weekTableVO = weekTable(weekTableDTO);
        BeanUtils.copyProperties(weekTableVO,result);
        return result;
    }

    @Override
    public TodayDutyStuListVO todayDutyStuList(TodayDutyStuListDTO dto) {
        TodayDutyStuListVO result = new TodayDutyStuListVO();

        ZrDateDO zrDateDO = zrDateRedisCache.get(DateUtil.parseDateTime(dto.getTime()));
        String xhStr = modelMapper.todayDutyStuList(zrDateDO, dto.getJsh());
        if (StringUtil.isBlank(xhStr)){
            return null;
        }
        List<String> xhList = Arrays.asList(xhStr.split(","));

        // 获取值日生基本信息
        SelectByXhListDTO selectByXhListDTO = new SelectByXhListDTO();
        selectByXhListDTO.setXhList(xhList);
        Wrapper<SelectByXhListVO> selectByXhListVOWrapper = stuInfoFeignApi.selectByXhList(selectByXhListDTO);
        if (selectByXhListVOWrapper.getCode() != 200) {
            throw new BusinessException("stuInfoFeignApi.selectByXhList()接口方法调用异常");
        }
        List<SelectByXhListItemVO> xsjbxxList = selectByXhListVOWrapper.getResult().getXsjbxxList();

        Map<String, ZrsItemVO> zrsMap = new HashMap<>();
        for (SelectByXhListItemVO zrs : xsjbxxList){
            ZrsItemVO itemVO= new ZrsItemVO();
            itemVO.setZrsxh(zrs.getXh());
            itemVO.setZrsxm(zrs.getXm());
            itemVO.setZp(zrs.getZp());
            zrsMap.put(zrs.getXh(),itemVO);
        }

        result.setZrsList(new ArrayList<>(zrsMap.values()));
        result.setTotalRows(zrsMap.size());
        return result;
    }

    public void insertSelective(ZhxyBjzr zhxyBjzr) {
        zhxyBjzr.setCjsj(new Date());
        zhxyBjzr.setXgsj(new Date());
        modelMapper.insertSelective(zhxyBjzr);
    }

    public Integer getWeek(LocalDate specifiedDate,LocalDate calculateDate) {
        // 如果今天是周一，则返回今天的日期；如果今天是周二到周日，则返回上周的周一‌
        LocalDate adjustedSpecifiedDate = specifiedDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 计算两个日期之间的天数差异
        long daysBetween = ChronoUnit.DAYS.between(specifiedDate, calculateDate);

        // 计算周数差异
        long weeksBetween = daysBetween / 7; // 整数除法，得到完整的周数
        long remainingDays = daysBetween % 7; // 计算剩余的天数

        // specifiedDate是否为周一
        long betweenMon = ChronoUnit.DAYS.between(adjustedSpecifiedDate, specifiedDate);
        if (betweenMon == 0){
            return Integer.valueOf(String.valueOf(weeksBetween)) + 1;
        } else {
            if ( (7 - remainingDays) <= betweenMon ){
                return Integer.valueOf(String.valueOf(weeksBetween)) + 2;
            }else {
                return Integer.valueOf(String.valueOf(weeksBetween)) + 1;
            }
        }
    }

    public DictDataInfoItemVO getZrx(String dictLabel,Integer i) {
        GetDataInfoDTO getDataInfoDTO = new GetDataInfoDTO();
        getDataInfoDTO.setPageNum(1);
        getDataInfoDTO.setPageSize(99);
        getDataInfoDTO.setDictType("bjzrxm");
        getDataInfoDTO.setDictLabel(dictLabel);
        Wrapper<GetDataInfoVO> getDataInfoVOWrapper = dictdataFeignApi.getDataInfo(getDataInfoDTO);
        if (getDataInfoVOWrapper.getCode() != 200) {
            throw new BusinessException("dictdataFeignApi.getDataInfo()接口方法调用异常");
        }
        List<DictDataInfoItemVO> dictDataInfoList = getDataInfoVOWrapper.getResult().getDictDataInfoList();
        if (CollectionUtils.isEmpty(dictDataInfoList)){
            throw new BusinessException("第" + i + "行数据值日项不存在，请先配置值日项字典");
        }
        DictDataInfoItemVO dictDataInfoItemVO = dictDataInfoList.get(0);
        return dictDataInfoItemVO;
    }

    public List<XnXqItemVO> getXnxq(Date ksrq,Date jsrq,Integer i) throws ParseException {
        if (jsrq.before(ksrq)){
            throw new BusinessException("第" + i + "行开始日期在结束日期之后，请修正日期数据");
        }
        GetXnXqByDateDTO getXnXqByDateDTO = new GetXnXqByDateDTO();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        getXnXqByDateDTO.setKsrq(sf.format(ksrq));
        getXnXqByDateDTO.setJsrq(sf.format(jsrq));
        Wrapper<GetXnXqByDateVO> getXnXqByDateVOWrapper = quarterFeignApi.getXnXqByDate(getXnXqByDateDTO);
        if (getXnXqByDateVOWrapper.getCode() != 200) {
            throw new BusinessException("quarterFeignApi.getXnXqByDate()接口方法调用异常");
        }

        GetXnXqByDateVO result = getXnXqByDateVOWrapper.getResult();
        List<XnXqItemVO> xnxqList = result.getXnxqList();
        if (CollectionUtils.isEmpty(xnxqList)){
            throw new BusinessException("无符合条件的学年学期");
        }
        return xnxqList;
    }

    public List<String> getZrsxh(String zrsxh,Integer i,Integer xq,String zrbbh){
        List<String> zrsxhList = Arrays.asList(zrsxh.split(","));
        // 判断值日生是否超过8个
        if (zrsxhList.size() > 8){
            throw new BusinessException("第" + i + "行，周" + xq +"值日生超过8个，不符合导入要求，请修改数据后重新导入");
        }
        // 判断值日生是否为本班学生
        for (String xh : zrsxhList){
            GetInfoDTO getInfoDTO = new GetInfoDTO();
            getInfoDTO.setPageNum(1);
            getInfoDTO.setPageSize(10);
            getInfoDTO.setSzbh(zrbbh);
            getInfoDTO.setXh(xh);
            Wrapper<GetInfoVO> getInfoVOWrapper = schoolrollinfoFeignApi.getInfo(getInfoDTO);
            if (getInfoVOWrapper.getCode() != 200) {
                throw new BusinessException("schoolrollinfoFeignApi.getInfo()接口方法调用异常");
            }
            List<SchoolRollSysItemVO> schoolRollSysList = getInfoVOWrapper.getResult().getSchoolRollSysList();
            if (CollectionUtils.isEmpty(schoolRollSysList)){
                throw new BusinessException("学号为" + xh + "的学生非值日班班级的学生，请重新填写正确的学号");
            }
        }
        return zrsxhList;
    }

    public Integer getKszc(Date ksrq,XnXqItemVO xnxq,Integer xq){
        LocalDate localDateKsrq = ksrq.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Integer kszc = getWeek(LocalDate.parse(xnxq.getKsrq()),localDateKsrq);
        // 当星期数不是周一时，判断开始周次和结束周次是否包含该星期数
        // 获取开始日期的星期数
        int ksxq = localDateKsrq.getDayOfWeek().getValue();
        if (ksxq > xq){
            kszc += 1;
        }
        return kszc;
    }
    public Integer getJszc(Date jsrq,XnXqItemVO xnxq,Integer xq){
        LocalDate localDateJsrq = jsrq.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Integer jszc = getWeek(LocalDate.parse(xnxq.getKsrq()),localDateJsrq);
        // 当星期数不是周一时，判断开始周次和结束周次是否包含该星期数
        // 获取结束日期的星期数
        int jsxq = localDateJsrq.getDayOfWeek().getValue();
        if (jsxq < xq){
            jszc -= 1;
        }
        return jszc;
    }

    public String getZrsxm(List<String> zrsxhList,Integer i){
        SelectByXhListDTO selectByXhListDTO = new SelectByXhListDTO();
        selectByXhListDTO.setXhList(zrsxhList);
        Wrapper<SelectByXhListVO> selectByXhListVOWrapper = stuInfoFeignApi.selectByXhList(selectByXhListDTO);
        if (selectByXhListVOWrapper.getCode() != 200) {
            throw new BusinessException("stuInfoFeignApi.selectByXhList()接口方法调用异常");
        }
        List<SelectByXhListItemVO> xsjbxxList = selectByXhListVOWrapper.getResult().getXsjbxxList();
        if (CollectionUtils.isEmpty(xsjbxxList)){
            throw new BusinessException("第" + i + "行数据学生学号无法查询到学生信息，请重新填入正确的学号");
        }
        String zrsxm = "";
        for (SelectByXhListItemVO item : xsjbxxList){
            zrsxm += item.getXm() + ",";
        }
        return zrsxm;
    }
}