/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service;

import com.xcwlkj.campus.model.dto.notice.DeleteZhxyTzByBhsDTO;
import com.xcwlkj.campus.model.dto.notice.InsertZhxyTzDTO;
import com.xcwlkj.campus.model.dto.notice.PublishZhxyTzDTO;
import com.xcwlkj.campus.model.dto.notice.RemoteZhxyTzDetailsDTO;
import com.xcwlkj.campus.model.dto.notice.RemoteZhxyTzListDTO;
import com.xcwlkj.campus.model.dto.notice.SelectZhxyTzByBhDTO;
import com.xcwlkj.campus.model.dto.notice.SelectZhxyTzListDTO;
import com.xcwlkj.campus.model.dto.notice.UnPublishZhxyTzDTO;
import com.xcwlkj.campus.model.dto.notice.UpdateZhxyTzDTO;
import com.xcwlkj.campus.model.vo.notice.RemoteZhxyTzDetailsVO;
import com.xcwlkj.campus.model.vo.notice.RemoteZhxyTzListVO;
import com.xcwlkj.campus.model.vo.notice.SelectZhxyTzByBhVO;
import com.xcwlkj.campus.model.vo.notice.SelectZhxyTzListVO;
import org.springframework.stereotype.Service;



/**
 * 校园通知表服务
 * <AUTHOR>
 * @version $Id: ZhxyTzService.java, v 0.1 2023年07月14日 11时23分 xcwlkj.com Exp $
 */
@Service
public interface ZhxyTzService  {

    /**
     * 新增校园通知
     *
     * @param insertZhxyTzDTO 校园通知
     * @return 结果
     */
    public void insertZhxyTz(InsertZhxyTzDTO insertZhxyTzDTO);


    void updateZhxyTz(UpdateZhxyTzDTO dto);

    void publishZhxyTz(PublishZhxyTzDTO dto);

    void unPublishZhxyTz(UnPublishZhxyTzDTO dto);

    void deleteZhxyTzByBhs(DeleteZhxyTzByBhsDTO dto);

    SelectZhxyTzListVO selectZhxyTzList(SelectZhxyTzListDTO dto);

    SelectZhxyTzByBhVO selectZhxyTzByBh(SelectZhxyTzByBhDTO dto);

    /**
     * 紧急通知/校园公告列表-外部服务
     *
     * @param dto
     * @return 结果
     */
    RemoteZhxyTzListVO remoteZhxyTzList(RemoteZhxyTzListDTO dto);

    /**
     * 紧急通知/校园公告详情-外部服务
     *
     * @param dto
     * @return 结果
     */
    RemoteZhxyTzDetailsVO remoteZhxyTzDetails(RemoteZhxyTzDetailsDTO dto);
}