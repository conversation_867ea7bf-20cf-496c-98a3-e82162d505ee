/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 校园相册照片/视频表
 * 
 * <AUTHOR>
 * @version $Id: ZhxyXczpsp.java, v 0.1 2023年07月20日 13时56分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "zhxy_xczpsp")
public class ZhxyXczpsp implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "bh")
    private Integer            bh;
    /** 相册编号 */
    @Column(name = "xcbh")
    private Integer            xcbh;
    /** 名称 */
    @Column(name = "mc")
    private String            mc;
    /** 类型
            图片image
            视频video */
    @Column(name = "lx")
    private String            lx;
    /** 图片/视频地址 */
    @Column(name = "tpspdz")
    private String            tpspdz;
    /** 创建时间 */
    @Column(name = "cjsj")
    private Date            cjsj;
    /** 更新时间 */
    @Column(name = "gxsj")
    private Date            gxsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


