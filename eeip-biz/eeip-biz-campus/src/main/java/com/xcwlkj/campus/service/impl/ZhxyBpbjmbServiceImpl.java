/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.biz.core.util.SbxxUtils;
import com.xcwlkj.campus.mapper.ZhxyBpbjbjMapper;
import com.xcwlkj.campus.mapper.ZhxyBpbjmbMapper;
import com.xcwlkj.campus.model.domain.ZhxyBpbjbj;
import com.xcwlkj.campus.model.domain.ZhxyBpbjmb;
import com.xcwlkj.campus.model.dos.LayoutDO;
import com.xcwlkj.campus.model.dos.LayoutItemDO;
import com.xcwlkj.campus.model.dos.LayoutPublishDO;
import com.xcwlkj.campus.model.dos.LayoutPublishItemDO;
import com.xcwlkj.campus.model.dto.bpbj.*;
import com.xcwlkj.campus.model.enums.PublishBizType;
import com.xcwlkj.campus.model.enums.PublishStatus;
import com.xcwlkj.campus.model.vo.bpbj.BjnrVO;
import com.xcwlkj.campus.model.vo.bpbj.MbDetailVO;
import com.xcwlkj.campus.model.vo.bpbj.MbItemVO;
import com.xcwlkj.campus.model.vo.bpbj.MbListVO;
import com.xcwlkj.campus.service.CampusBoardOperateService;
import com.xcwlkj.campus.service.ZhxyBpbjmbService;
import com.xcwlkj.campus.service.ZhxyFbfwService;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.utils.RequestUtil;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 班牌布局模板表服务
 * <AUTHOR>
 * @version $Id: ZhxyBpbjmbServiceImpl.java, v 0.1 2023年07月19日 09时15分 xcwlkj.com Exp $
 */
@Service("zhxyBpbjmbService")
public class ZhxyBpbjmbServiceImpl  implements ZhxyBpbjmbService  {

    @Resource
    private ZhxyBpbjmbMapper modelMapper;
    @Resource
	private SbxxUtils sbxxUtils;
    @Resource
	private CampusBoardOperateService campusBoardOperateService;
    @Resource
	private ZhxyFbfwService zhxyFbfwService;
    @Resource
	private ZhxyBpbjbjMapper zhxyBpbjbjMapper;
    

    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbAdd(com.xcwlkj.campus.model.dto.bpbj.MbAddDTO)
     */
	@Override
	public void mbAdd(MbAddDTO dto) {

		ZhxyBpbjmb zhxyBpbjmb = new ZhxyBpbjmb();
		zhxyBpbjmb.setMbmc(dto.getMbmc());
		zhxyBpbjmb.setBjtp(dto.getBjtp());
		zhxyBpbjmb.setMs(dto.getMs());
		zhxyBpbjmb.setBjnr(JSONObject.toJSONString(dto.getBjnr()));
		zhxyBpbjmb.setCjsj(new Date());
		zhxyBpbjmb.setGxsj(new Date());
		zhxyBpbjmb.setZt(PublishStatus.UNPUBLISHED.getValue());

		modelMapper.insertSelective(zhxyBpbjmb);

	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbList(com.xcwlkj.campus.model.dto.bpbj.MbListDTO)
     */
	@Override
	public MbListVO mbList(MbListDTO dto) {

		MbListVO result = new MbListVO();
		Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		Example example = new Example(ZhxyBpbjmb.class);
		Example.Criteria cr = example.createCriteria();
		if(StringUtil.isNotBlank(dto.getMbmc())) {
			cr.andLike("mbmc","%" + dto.getMbmc()+"%");
		}
		if(dto.getZt() != null ) {
			cr.andEqualTo("zt",dto.getZt());
		}
		List<ZhxyBpbjmb> list = modelMapper.selectByExample(example);
		result.setTotalRows((int)page.getTotal());
		result.setMbList(new ArrayList<>());
		for(ZhxyBpbjmb model : list) {
			MbItemVO itemVO = new MbItemVO();
			itemVO.setBh(model.getBh());//编号
			itemVO.setMbmc(model.getMbmc());//模板名称
			itemVO.setBjtp(model.getBjtp());//背景图片
			itemVO.setZt(model.getZt());
			itemVO.setMs(model.getMs());
			result.getMbList().add(itemVO);
		}
		return result;

	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbModify(com.xcwlkj.campus.model.dto.bpbj.MbModifyDTO)
     */
	@Override
	public void mbModify(MbModifyDTO dto) {

		ZhxyBpbjmb zhxyBpbjmb = new ZhxyBpbjmb();
		zhxyBpbjmb.setBh(dto.getBh());
		zhxyBpbjmb.setMbmc(dto.getMbmc());
		zhxyBpbjmb.setBjtp(dto.getBjtp());
		zhxyBpbjmb.setMs(dto.getMs());
		zhxyBpbjmb.setBjnr(JSONObject.toJSONString(dto.getBjnr()));
		zhxyBpbjmb.setGxsj(new Date());
		zhxyBpbjmb.setZt(PublishStatus.UNPUBLISHED.getValue());

		modelMapper.updateByPrimaryKeySelective(zhxyBpbjmb);
	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbPublish(com.xcwlkj.campus.model.dto.bpbj.MbPublishDTO)
     */
	@Override
	public void mbPublish(MbPublishDTO dto) {

		ZhxyBpbjmb zhxyBpbjmb = modelMapper.selectByPrimaryKey(dto.getBh());
		publish(zhxyBpbjmb, dto.getBjjshList(), dto.getBjjsmcList(), dto.getBjjsbjList());

		modelMapper.updateByPrimaryKeySelective(zhxyBpbjmb);
	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbDelete(com.xcwlkj.campus.model.dto.bpbj.MbDeleteDTO)
     */
	@Override
	public void mbDelete(MbDeleteDTO dto) {
	
		Example example = new Example(ZhxyBpbjmb.class);
		example.createCriteria()
				.andIn("bh", dto.getBhList());

		modelMapper.deleteByExample(example);
	}

	private ZhxyBpbjmb publish(ZhxyBpbjmb bpbjmb, List<String> bjjshList, List<String> bjjsmcList, List<String> bjjsbjList){

		Map<String,	Object> properties = new HashMap<>();
		LayoutDO layoutDO = JSONObject.parseObject(bpbjmb.getBjnr(), LayoutDO.class);
		List<Integer> componentIds = layoutDO.getItems().stream()
				.map(LayoutItemDO::getComponentId)
				.distinct()
				.collect(Collectors.toList());
		Example emBjbj = new Example(ZhxyBpbjbj.class);
		emBjbj.createCriteria()
				.andIn("bh", componentIds);
		List<ZhxyBpbjbj> zhxyBpbjbjs = zhxyBpbjbjMapper.selectByExample(emBjbj);
		Map<Integer, ZhxyBpbjbj> bjbjMap = zhxyBpbjbjs.stream().collect(Collectors.toMap(ZhxyBpbjbj::getBh, Function.identity()));
		LayoutPublishDO layoutPublishDO = new LayoutPublishDO();
		layoutPublishDO.setType(layoutDO.getType());
		layoutPublishDO.setRows(layoutDO.getRows());
		layoutPublishDO.setColumns(layoutDO.getColumns());
		layoutPublishDO.setBackground(bpbjmb.getBjtp() == null?"":bpbjmb.getBjtp());
		List<LayoutPublishItemDO> layoutPublishItemDOS = new ArrayList<>();
		for (LayoutItemDO item : layoutDO.getItems()) {
			LayoutPublishItemDO layoutPublishItemDO = new LayoutPublishItemDO();
			layoutPublishItemDO.setCol(item.getCol());
			layoutPublishItemDO.setRow(item.getRow());
			if (bjbjMap.containsKey(item.getComponentId())){
				ZhxyBpbjbj zhxyBpbjbj = bjbjMap.get(item.getComponentId());
				layoutPublishItemDO.setComponentType(zhxyBpbjbj.getLx());
				layoutPublishItemDO.setSize(zhxyBpbjbj.getCc());
			}
			layoutPublishItemDOS.add(layoutPublishItemDO);
		}
		layoutPublishDO.setItems(layoutPublishItemDOS);

		JSONObject jsonObject = new JSONObject();
		jsonObject.put("layout", JSON.parseObject(JSON.toJSONString(layoutPublishDO, SerializerFeature.WriteMapNullValue)));

		properties.put("homepage_layout", jsonObject);
		campusBoardOperateService.writeProperties(sbxxUtils.getDevicesSupplier(bjjshList, "4869"), properties);

		zhxyFbfwService.deleteZhxyFbfwByYwbh(PublishBizType.LAYOUT, (long) bpbjmb.getBh());
		// 删除教室绑定的其他布局
		zhxyFbfwService.deleteZhxyFbfwByJsh(PublishBizType.LAYOUT, bjjshList);
		zhxyFbfwService.batchZhxyFbfw(bjjshList, bjjsmcList, bjjsbjList, PublishBizType.LAYOUT, bpbjmb.getBh());

		// 发布信息更新
		XcSsoUser loginUser = RequestUtil.getLoginUser();
		bpbjmb.setZt(PublishStatus.PUBLISHED.getValue());
		bpbjmb.setFbr(loginUser.getUserId());
		bpbjmb.setFbrmc(loginUser.getUserName());
		bpbjmb.setFbsj(new Date());
		bpbjmb.setGxsj(new Date());

		return bpbjmb;
	}
    /** 
     * @see com.xcwlkj.campus.service.ZhxyBpbjmbService#mbDetail(com.xcwlkj.campus.model.dto.bpbj.MbDetailDTO)
     */
	@Override
	public MbDetailVO mbDetail(MbDetailDTO dto) {
		MbDetailVO result = new MbDetailVO();
		ZhxyBpbjmb zhxyBpbjmb = modelMapper.selectByPrimaryKey(dto.getBh());
		result.setBjnr(JSONObject.parseObject(zhxyBpbjmb.getBjnr(), BjnrVO.class));
		List<String> jshList = zhxyFbfwService.selectJshList(PublishBizType.LAYOUT, (long) dto.getBh());
		result.setJshList(jshList);
		return result;
	}
}