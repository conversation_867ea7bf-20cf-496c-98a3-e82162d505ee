---
title: new_xiaoji_check_platform
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# new_xiaoji_check_platform

Base URLs:

# Authentication

# 发送核验数据到上级

## POST 考生核验信息

POST /ksyw/revIdvData/setValidateInfo

encrptJson解密后字符串如下
{
  "exam": {
    "examPlanCode": "考试编号",
    "examSeqCode": "场次编号"
  },
  "hyxx": [
    {
      "yzsj": "验证时间yyyy-MM-dd HH:mm:ss",
      "hyjg": "核验结果 1-通过 0-不通过",
      "zkzh": "准考证号",
      "kddm": "考点代码",
      "kch": "考场号",
      "szjg": "刷证结果 1-通过 0-不通过",
      "rlsbjg": "人脸识别结果 1-通过 0-不通过",
      "zwrzjg": "指纹认证结果 1-通过 0-不通过",
      "xsd": "相似度",
      "sjyxj": "数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场"
    }
  ]
}

> Body 请求参数

```json
{
  "encrptJson": "string",
  "sbxlh": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» encrptJson|body|string| 是 | 字符串需要sm4加密|使用固定秘钥hssfhy@2025#@!|
|» sbxlh|body|string| 是 | 设备序列号|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 考场入场人工（入场、缺考）

POST /ksyw/revIdvData/setValidateStuEnter

encrptJson解密后字符串
{
  "rcxx": [
    {
      "sbsj": "上报时间yyyy-MM-dd HH:mm:ss",
      "zkzh": "准考证号",
      "kddm": "考点代码",
      "kch": "考场号",
      "rczt": " 入场状态 0-人工审核不通过，1-人工审核通过，2-缺考",
      "xsd": "相识度",
      "sjyxj": "数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场"
    }
  ],
  "exam": {
    "examPlanCode": "考试编号",
    "examSeqCode": "场次编号"
  }
}

> Body 请求参数

```json
{
  "encrptJson": "string",
  "sbxlh": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» encrptJson|body|string| 是 | 字符串需要sm4加密|使用固定秘钥hssfhy@2025#@!|
|» sbxlh|body|string| 是 | 设备序列号|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 考生照片上传

POST /ksyw/revIdvData/setValidatePic

encrptJson解密后
{
  "hyxx": [
    {
      "zkzh": "准考证号",
      "kddm": "考点代码",
      "kch": "考场号",
      "xczp": "上传上级平台文件服务器现场照片id",
      "sfzzp": "上传上级平台文件服务器身份证照片id"
    }
  ],
  "exam": {
    "examPlanCode": "考试编号",
    "examSeqCode": "场次编号"
  }
}

> Body 请求参数

```json
{
  "encrptJson": "string",
  "sbxlh": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» encrptJson|body|string| 是 | 字符串需要sm4加密|使用固定秘钥hssfhy@2025#@!|
|» sbxlh|body|string| 是 | 设备序列号|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

