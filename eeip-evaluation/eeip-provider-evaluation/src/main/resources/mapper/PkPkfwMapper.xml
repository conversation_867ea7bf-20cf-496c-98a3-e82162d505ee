<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.evaluation.mapper.PkPkfwMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.evaluation.model.domain.PkPkfw">
        <id column="rwbh" jdbcType="VARCHAR" property="rwbh" />
        <id column="fwbh" jdbcType="VARCHAR" property="fwbh" />
        <result column="rwmc" jdbcType="VARCHAR" property="rwmc" />
        <result column="fwmc" jdbcType="VARCHAR" property="fwmc" />
        <result column="fwlx" jdbcType="VARCHAR" property="fwlx" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        rwbh,
        fwbh,
        rwmc,
        fwmc,
        fwlx

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="rwbh != null and rwbh != ''">
            AND rwbh = #{rwbh,jdbcType=VARCHAR}
        </if>
        <if test="fwbh != null and fwbh != ''">
            AND fwbh = #{fwbh,jdbcType=VARCHAR}
        </if>
        <if test="rwmc != null and rwmc != ''">
            AND rwmc = #{rwmc,jdbcType=VARCHAR}
        </if>
        <if test="fwmc != null and fwmc != ''">
            AND fwmc = #{fwmc,jdbcType=VARCHAR}
        </if>
        <if test="fwlx != null and fwlx != ''">
            AND fwlx = #{fwlx,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="rwbh != null ">
            rwbh = #{rwbh,jdbcType=VARCHAR},
        </if>
        <if test="fwbh != null ">
            fwbh = #{fwbh,jdbcType=VARCHAR},
        </if>
        <if test="rwmc != null ">
            rwmc = #{rwmc,jdbcType=VARCHAR},
        </if>
        <if test="fwmc != null ">
            fwmc = #{fwmc,jdbcType=VARCHAR},
        </if>
        <if test="fwlx != null ">
            fwlx = #{fwlx,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

    <insert id="batchInsert">
        insert into pk_pkfw  (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" item="pkfw" separator=",">
            (
            #{pkfw.rwbh},
            #{pkfw.fwbh},
            #{pkfw.rwmc},
            #{pkfw.fwmc},
            #{pkfw.fwlx}
            )
        </foreach>
    </insert>

</mapper>
