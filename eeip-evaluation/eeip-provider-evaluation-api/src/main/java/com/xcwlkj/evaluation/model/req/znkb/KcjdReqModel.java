/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.req.znkb;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 课程进度请求
 * <AUTHOR>
 * @version $Id: KcjdReqModel.java, v 0.1 2023年03月02日 10时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KcjdReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 学年 */
    @NotBlank(message = "学年不能为空")
    private String xn;
    /** 学期码 */
    @NotBlank(message = "学期码不能为空")
    private String xqm;
    /** 学期开始日期 */
    @NotBlank(message = "学期开始日期不能为空")
    private String xqksrq;
    /** 学期结束日期 */
    @NotBlank(message = "学期结束日期不能为空")
    private String xqjsrq;
    /** 查询日期 */
    private String cxrq;
    /** 课程名称 */
    private String kcmc;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}