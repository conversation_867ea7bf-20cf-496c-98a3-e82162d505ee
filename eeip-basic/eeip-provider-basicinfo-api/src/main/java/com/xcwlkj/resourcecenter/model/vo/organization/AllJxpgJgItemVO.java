/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.model.vo.organization;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;


/**
 * 教学评估树列表vo
 *
 * <AUTHOR>
 * @version $Id: JgItemVO.java, v 0.1 2023年05月09日 08时58分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AllJxpgJgItemVO implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 机构编号
     */
    private String jgid;
    /**
     * 机构名称
     */
    private String jgmc;
    /**
     * 机构类型（不传时返回根节点信息）：xx(学校) | xq（校区）| dw(单位) | yx（院系）| js（教室）| sxj(摄像机)
     */
    private String jglx;
    /**
     * 所属机构编号
     */
    private String ssjgid;
    /**
     * 是否根节点
     */
    private String isRoot;
    /**
     * 是否叶节点
     */
    private String isLeaf;

    /**
     * 设备状态
     */
    private String lxzt;
    /**
     * 网络地址
     */
    private String wldz;
    /**
     * 是否默认
     */
    private String isDefault;


    private List<SbxxItemVO> sbList;

    private List<AllJxpgJgItemVO> jgList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
