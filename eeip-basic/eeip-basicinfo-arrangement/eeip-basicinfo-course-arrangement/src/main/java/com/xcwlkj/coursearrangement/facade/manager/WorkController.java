/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.coursearrangement.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.coursearrangement.model.dto.work.*;
import com.xcwlkj.coursearrangement.model.req.work.*;
import com.xcwlkj.coursearrangement.model.resp.work.*;
import com.xcwlkj.coursearrangement.model.vo.work.WorkBindingDetailByJshVO;
import com.xcwlkj.coursearrangement.model.vo.work.WorkBindingDetailVO;
import com.xcwlkj.coursearrangement.model.vo.work.WorkDetailVO;
import com.xcwlkj.coursearrangement.model.vo.work.WorkListVO;
import com.xcwlkj.coursearrangement.service.JcxxTscszxService;
import com.xcwlkj.coursearrangement.service.JcxxZxService;
import com.xcwlkj.coursearrangement.service.JcxxZxjcService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Work控制层
 * <AUTHOR>
 * @version $Id: WorkController.java, v 0.1 2022年10月08日 09时36分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("WorkManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class WorkController extends BaseController {

    @Resource
    private JcxxZxService jcxxZxService;
    @Resource
    private JcxxZxjcService jcxxZxjcService;
    @Resource
    private JcxxTscszxService jcxxTscszxService;

    /**
     * 列表-作息
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/manager/coursearrangement/v1/work/list")
    public Wrapper<WorkListRespModel> workList(@Validated @RequestBody WorkListReqModel reqModel) {
        log.info("收到请求开始：[列表-作息][/manager/coursearrangement/v1/work/list]reqModel:" + reqModel.toString());
        WorkListDTO dto = new WorkListDTO();
        dto.setZxzt(reqModel.getZxzt());
        dto.setZxmc(reqModel.getZxmc());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        WorkListVO result = jcxxZxService.workList(dto);
        WorkListRespModel respModel = new WorkListRespModel();
        respModel.setZxList(result.getZxList());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[列表-作息][/manager/coursearrangement/v1/work/list]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 详情-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:detail")
    @PostMapping(value = "/manager/coursearrangement/v1/work/detail")
    public Wrapper<WorkDetailRespModel> workDetail(@Validated @RequestBody WorkDetailReqModel reqModel) {
        log.info("收到请求开始：[详情-作息][/manager/coursearrangement/v1/work/detail]reqModel:" + reqModel.toString());
        WorkDetailDTO dto = new WorkDetailDTO();
        dto.setBh(reqModel.getBh());
        WorkDetailVO result = jcxxZxService.workDetail(dto);
        WorkDetailRespModel respModel = new WorkDetailRespModel();
        respModel.setZxjcList(result.getZxjcList());
        log.info("处理请求结束：[详情-作息][/manager/coursearrangement/v1/work/detail]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 添加-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:add")
    @PostMapping(value = "/manager/coursearrangement/v1/work/add")
    public Wrapper<WorkAddRespModel> workAdd(@Validated @RequestBody WorkAddReqModel reqModel) {
        log.info("收到请求开始：[添加-作息][/manager/coursearrangement/v1/work/add]reqModel:" + reqModel.toString());
        WorkAddDTO dto = new WorkAddDTO();
        dto.setZxmc(reqModel.getZxmc());
        dto.setZxzt(reqModel.getZxzt());
        jcxxZxService.workAdd(dto);
        WorkAddRespModel respModel = new WorkAddRespModel();

        log.info("处理请求结束：[添加-作息][/manager/coursearrangement/v1/work/add]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 删除-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:delete")
    @PostMapping(value = "/manager/coursearrangement/v1/work/delete")
    public Wrapper<WorkDeleteRespModel> workDelete(@Validated @RequestBody WorkDeleteReqModel reqModel) {
        log.info("收到请求开始：[删除-作息][/manager/coursearrangement/v1/work/delete]reqModel:" + reqModel.toString());
        WorkDeleteDTO dto = new WorkDeleteDTO();
        dto.setBhList(reqModel.getBhList());
        jcxxZxService.workDelete(dto);
        WorkDeleteRespModel respModel = new WorkDeleteRespModel();

        log.info("处理请求结束：[删除-作息][/manager/coursearrangement/v1/work/delete]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 修改子项-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:updateItem")
    @PostMapping(value = "/manager/coursearrangement/v1/work/updateItem")
    public Wrapper<WorkUpdateItemRespModel> workUpdateItem(@Validated @RequestBody WorkUpdateItemReqModel reqModel) {
        log.info("收到请求开始：[修改子项-作息][/manager/coursearrangement/v1/work/updateItem]reqModel:" + reqModel.toString());
        WorkUpdateItemDTO dto = new WorkUpdateItemDTO();
        dto.setBh(reqModel.getBh());
        dto.setJc(reqModel.getJc());
        dto.setStartTime(reqModel.getStartTime());
        dto.setEndTime(reqModel.getEndTime());
        jcxxZxjcService.workUpdateItem(dto);
        WorkUpdateItemRespModel respModel = new WorkUpdateItemRespModel();

        log.info("处理请求结束：[修改子项-作息][/manager/coursearrangement/v1/work/updateItem]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 添加子项-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:addItem")
    @PostMapping(value = "/manager/coursearrangement/v1/work/addItem")
    public Wrapper<WorkAddItemRespModel> workAddItem(@Validated @RequestBody WorkAddItemReqModel reqModel) {
        log.info("收到请求开始：[添加子项-作息][/manager/coursearrangement/v1/work/addItem]reqModel:" + reqModel.toString());
        WorkAddItemDTO dto = new WorkAddItemDTO();
        dto.setJc(reqModel.getJc());
        dto.setZxbh(reqModel.getZxbh());
        dto.setStartTime(reqModel.getStartTime());
        dto.setEndTime(reqModel.getEndTime());
        jcxxZxjcService.workAddItem(dto);
        WorkAddItemRespModel respModel = new WorkAddItemRespModel();

        log.info("处理请求结束：[添加子项-作息][/manager/coursearrangement/v1/work/addItem]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 删除子项-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:deleteItem")
    @PostMapping(value = "/manager/coursearrangement/v1/work/deleteItem")
    public Wrapper<WorkDeleteItemRespModel> workDeleteItem(@Validated @RequestBody WorkDeleteItemReqModel reqModel) {
        log.info("收到请求开始：[删除子项-作息][/manager/coursearrangement/v1/work/deleteItem]reqModel:" + reqModel.toString());
        WorkDeleteItemDTO dto = new WorkDeleteItemDTO();
        dto.setBhList(reqModel.getBhList());
        dto.setZxbh(reqModel.getZxbh());
        jcxxZxjcService.workDeleteItem(dto);
        WorkDeleteItemRespModel respModel = new WorkDeleteItemRespModel();

        log.info("处理请求结束：[删除子项-作息][/manager/coursearrangement/v1/work/deleteItem]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 修改-作息
     *
     * @param reqModel
     * @return
     */
	@Permission("work:update")
    @PostMapping(value = "/manager/coursearrangement/v1/work/update")
    public Wrapper<WorkUpdateRespModel> workUpdate(@Validated @RequestBody WorkUpdateReqModel reqModel) {
        log.info("收到请求开始：[修改-作息][/manager/coursearrangement/v1/work/update]reqModel:" + reqModel.toString());
        WorkUpdateDTO dto = new WorkUpdateDTO();
        dto.setBh(reqModel.getBh());
        dto.setZxmc(reqModel.getZxmc());
        dto.setZxzt(reqModel.getZxzt());
        jcxxZxService.workUpdate(dto);
        WorkUpdateRespModel respModel = new WorkUpdateRespModel();

        log.info("处理请求结束：[修改-作息][/manager/coursearrangement/v1/work/update]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 教室作息绑定
     * @param reqModel
     * @return
     */
	@Permission("work:bindJsh")
    @PostMapping(value = "/manager/coursearrangement/v1/work/bindJsh")
    public Wrapper<WorkBindJshRespModel> workBindJsh(@RequestBody WorkBindJshReqModel reqModel) {
        log.info("收到请求开始：[教室作息绑定][/manager/eeip/coursearrangement/v1/work/bindJsh]reqModel:"+reqModel.toString());
        WorkBindJshDTO dto = new WorkBindJshDTO();
        dto.setZxbh(reqModel.getZxbh());
        dto.setJshList(reqModel.getJshList());
        jcxxTscszxService.workBindJsh(dto);
        WorkBindJshRespModel respModel = new WorkBindJshRespModel();
        log.info("处理请求结束：[教室作息绑定][/manager/eeip/coursearrangement/v1/work/bindJsh]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
         return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 教室作息绑定详情
     * @param reqModel
     * @return
     */
	@Permission("work:bindingDetail")
    @PostMapping(value = "/manager/coursearrangement/v1/work/bindingDetail")
    public Wrapper<WorkBindingDetailRespModel> workBindingDetail(@RequestBody WorkBindingDetailReqModel reqModel) {
        log.info("收到请求开始：[教室作息绑定详情][/manager/eeip/coursearrangement/v1/work/bindingDetail]reqModel:"+reqModel.toString());
        WorkBindingDetailDTO dto = new WorkBindingDetailDTO();
        dto.setZxbh(reqModel.getZxbh());
        WorkBindingDetailVO workBindingDetailVO = jcxxTscszxService.workBindingDetail(dto);
        WorkBindingDetailRespModel respModel = new WorkBindingDetailRespModel();
        respModel.setJshList(workBindingDetailVO.getJshList());
        log.info("处理请求结束：[教室作息绑定详情][/manager/eeip/coursearrangement/v1/work/bindingDetail]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 根据教室号查询教室作息绑定详情
     * @param reqModel
     * @return
     */
	@Permission("lesson:bindingWorkDetailByJsh")
    @PostMapping(value = "/manager/coursearrangement/v1/work/bindingDetailByJsh")
    public Wrapper<WorkBindingDetailByJshRespModel> workBindingDetailByJsh(@RequestBody WorkBindingDetailByJshReqModel reqModel) {
        log.info("收到请求开始：[根据教室号查询教室作息绑定详情][/manager/eeip/coursearrangement/v1/work/bindingDetailByJsh]reqModel:"+reqModel.toString());
        WorkBindingDetailByJshDTO dto = new WorkBindingDetailByJshDTO();
        dto.setJsh(reqModel.getJsh());
        WorkBindingDetailByJshVO workBindingDetailByJshVO = jcxxZxjcService.workBindingDetailByJsh(dto);
        WorkBindingDetailByJshRespModel respModel = new WorkBindingDetailByJshRespModel();
        respModel.setZxjcList(workBindingDetailByJshVO.getZxjcList());
        log.info("处理请求结束：[根据教室号查询教室作息绑定详情][/manager/eeip/coursearrangement/v1/work/bindingDetailByJsh]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
}