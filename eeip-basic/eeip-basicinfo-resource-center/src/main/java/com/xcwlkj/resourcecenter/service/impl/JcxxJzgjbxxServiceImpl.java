/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.resourcecenter.mapper.JcxxJzgjbxxMapper;
import com.xcwlkj.resourcecenter.mapper.JcxxYxsdwjbxxMapper;
import com.xcwlkj.resourcecenter.model.domain.JcxxJzgjbxx;
import com.xcwlkj.resourcecenter.model.domain.JcxxYxsdwjbxx;
import com.xcwlkj.resourcecenter.model.dto.teacher.AddV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.DeleteV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.DetailsForRegisterV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.DetailsV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.InfoByGhDTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.ListByGhV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.ListV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.ModifyV1DTO;
import com.xcwlkj.resourcecenter.model.dto.teacher.SysListV1DTO;
import com.xcwlkj.resourcecenter.model.enums.JglxEnum;
import com.xcwlkj.resourcecenter.model.vo.teacher.DetailsForRegisterV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.DetailsV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.InfoByGhVO;
import com.xcwlkj.resourcecenter.model.vo.teacher.JzgItemVO;
import com.xcwlkj.resourcecenter.model.vo.teacher.ListByGhV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.ListV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.SysListV1VO;
import com.xcwlkj.resourcecenter.model.vo.teacher.SysTeacherItemVO;
import com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService;
import com.xcwlkj.resourcecenter.service.JcxxYxsdwjbxxService;
import com.xcwlkj.resourcecenter.util.AttachmentUtils;
import com.xcwlkj.resourcecenter.util.ZipUtils;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdcardUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.utils.IDCardUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * jcxx_jzgjbxx服务
 * <AUTHOR>
 * @version $Id: JcxxJzgjbxxServiceImpl.java, v 0.1 2022年09月27日 15时00分 xcwlkj.com Exp $
 */
@Service("jcxxJzgjbxxService")
public class JcxxJzgjbxxServiceImpl extends BaseServiceImpl<JcxxJzgjbxxMapper, JcxxJzgjbxx> implements JcxxJzgjbxxService  {

    @Resource
    private JcxxJzgjbxxMapper modelMapper;
    @Resource
	private JcxxYxsdwjbxxService jcxxYxsdwjbxxService;
    @Resource
	private JcxxYxsdwjbxxMapper jcxxYxsdwjbxxMapper;
    @Resource
	private AttachmentUtils attachmentUtils;
    

    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#addV1(com.xcwlkj.resourcecenter.model.dto.teacher.AddV1DTO)
     */
	@Override
	public void addV1(AddV1DTO dto) {

		JcxxJzgjbxx jcxxJzgjbxx = new JcxxJzgjbxx();
		BeanUtil.copyProperties(dto, jcxxJzgjbxx);
		if (StringUtil.isNotBlank(dto.getDwh())){
			JcxxYxsdwjbxx jcxxYxsdwjbxx = jcxxYxsdwjbxxMapper.selectByPrimaryKey(dto.getDwh());
			if (jcxxYxsdwjbxx != null){
				jcxxJzgjbxx.setXqh(jcxxYxsdwjbxx.getXqh());
			}else {
				throw new BusinessException("该单位不存在");
			}
		}
		jcxxJzgjbxx.setCsrq(StringUtil.isNotBlank(dto.getCsrq())?DateUtil.parseDate(dto.getCsrq()):null);//出生日期
		jcxxJzgjbxx.setSfzjyxq(StringUtil.isNotBlank(dto.getSfzjyxq())?DateUtil.parseDate(dto.getSfzjyxq()):null);//身份证件有效期
		jcxxJzgjbxx.setLxrq(StringUtil.isNotBlank(dto.getLxrq())?DateUtil.parseDate(dto.getLxrq()):null);//来校日期
		jcxxJzgjbxx.setCjsj(new Date());//创建时间
		jcxxJzgjbxx.setXgsj(new Date());//修改时间
		modelMapper.insertSelective(jcxxJzgjbxx);

	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#modifyV1(com.xcwlkj.resourcecenter.model.dto.teacher.ModifyV1DTO)
     */
	@Override
	public void modifyV1(ModifyV1DTO dto) {

		JcxxJzgjbxx jcxxJzgjbxx = new JcxxJzgjbxx();
		BeanUtil.copyProperties(dto, jcxxJzgjbxx);
		if (StringUtil.isNotBlank(dto.getDwh())){
			JcxxYxsdwjbxx jcxxYxsdwjbxx = jcxxYxsdwjbxxMapper.selectByPrimaryKey(dto.getDwh());
			if (jcxxYxsdwjbxx != null){
				jcxxJzgjbxx.setXqh(StringUtil.isNotBlank(jcxxYxsdwjbxx.getXqh())? jcxxYxsdwjbxx.getXqh():"");
			}else {
				throw new BusinessException("该单位不存在");
			}
		}
		if (jcxxJzgjbxx.getSfzjh() == null){
			jcxxJzgjbxx.setSfzjh("");
		}
		// 身份证校验,不符合条件不修改
		if (!IdcardUtil.validateCard(jcxxJzgjbxx.getSfzjh())){
			jcxxJzgjbxx.setSfzjh(null);
		}
		jcxxJzgjbxx.setCsrq(StringUtil.isNotBlank(dto.getCsrq())?DateUtil.parseDate(dto.getCsrq()):null);//出生日期
		jcxxJzgjbxx.setSfzjyxq(StringUtil.isNotBlank(dto.getSfzjyxq())?DateUtil.parseDate(dto.getSfzjyxq()):null);//身份证件有效期
		jcxxJzgjbxx.setLxrq(StringUtil.isNotBlank(dto.getLxrq())?DateUtil.parseDate(dto.getLxrq()):null);//来校日期
		jcxxJzgjbxx.setXgsj(new Date());//修改时间
		modelMapper.updateByPrimaryKeySelective(jcxxJzgjbxx);
	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#deleteV1(com.xcwlkj.resourcecenter.model.dto.teacher.DeleteV1DTO)
     */
	@Override
	public void deleteV1(DeleteV1DTO dto) {

		Example example = new Example(JcxxJzgjbxx.class);
		example.createCriteria()
				.andIn("gh", dto.getGhList());
		modelMapper.deleteByExample(example);
	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#listV1(com.xcwlkj.resourcecenter.model.dto.teacher.ListV1DTO)
     */
	@Override
	public ListV1VO listV1(ListV1DTO dto) {

		ListV1VO result = new ListV1VO();
		PageHelper.startPage( dto.getPageNum(), dto.getPageSize());
		List<JzgItemVO> list = modelMapper.listV1(dto);
		for (JzgItemVO jzgItemVO : list) {
			jzgItemVO.setSfzjh(IDCardUtils.hide(jzgItemVO.getSfzjh(), 8));
		}
		PageInfo<JzgItemVO> pageInfo = new PageInfo<JzgItemVO>(list);
		result.setTotalRows((int)pageInfo.getTotal());
		result.setJzgList(list);


		return result;

	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#detailsV1(com.xcwlkj.resourcecenter.model.dto.teacher.DetailsV1DTO)
     */
	@Override
	public DetailsV1VO detailsV1(DetailsV1DTO dto) {
		DetailsV1VO result = modelMapper.detailsV1(dto);
		if (result != null) {
			result.setSfzjh(IDCardUtils.hide(result.getSfzjh(), 8));
			result.setZpUrl(result.getZp() != null ? attachmentUtils.getUrl(result.getZp()) : "");//照片
		}else {
			result = new DetailsV1VO();
		}
		return result;

	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#sysListV1(SysListV1DTO)
     */
	@Override
	public SysListV1VO sysListV1(SysListV1DTO dto) {
		SysListV1VO result = new SysListV1VO();

		Example example = new Example(JcxxJzgjbxx.class);
		Example.Criteria criteria = example.createCriteria();

		if (StringUtil.isNotBlank(dto.getJglx())) {
			List<String> jgids = Arrays.asList(dto.getJgid().split(","));
			switch (JglxEnum.get(dto.getJglx())) {
				case XQ:
					criteria.andIn("xqh", jgids);
					break;
				case YX:
					criteria.andIn("dwh", jgids);
					break;
				default:
					break;
			}
		}

		if (StringUtil.isNotBlank(dto.getXm())) {
			criteria.andLike("xm", "%" + dto.getXm() + "%");
		}
		if (StringUtil.isNotBlank(dto.getGh())){
			criteria.andEqualTo("gh", dto.getGh());
		}

		PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		List<JcxxJzgjbxx> jcxxJzgjbxxes = modelMapper.selectByExample(example);

		List<String> dwhs = jcxxJzgjbxxes.stream().map(JcxxJzgjbxx::getDwh).distinct().collect(Collectors.toList());
		Map<String, String> dwmcMap = jcxxYxsdwjbxxService.getDwmcMap(dwhs);
		PageInfo<JcxxJzgjbxx> pageInfo = new PageInfo<>(jcxxJzgjbxxes);
		result.setTotalRows((int) pageInfo.getTotal());
		result.setTeacherList(new ArrayList<>());

		for (JcxxJzgjbxx jcxxJzgjbxx : jcxxJzgjbxxes) {
			SysTeacherItemVO sysTeacherItemVO = new SysTeacherItemVO();
			sysTeacherItemVO.setGh(jcxxJzgjbxx.getGh());
			sysTeacherItemVO.setDwh(jcxxJzgjbxx.getDwh());
			sysTeacherItemVO.setDwmc(dwmcMap.getOrDefault(jcxxJzgjbxx.getDwh(), ""));
			sysTeacherItemVO.setXm(jcxxJzgjbxx.getXm());
			result.getTeacherList().add(sysTeacherItemVO);
		}
		return result;
	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#detailsForRegisterV1(com.xcwlkj.resourcecenter.model.dto.teacher.DetailsForRegisterV1DTO)
     */
	@Override
	public DetailsForRegisterV1VO detailsForRegisterV1(DetailsForRegisterV1DTO dto) {
		DetailsForRegisterV1VO result = new DetailsForRegisterV1VO();
		JcxxJzgjbxx jcxxJzgjbxx = modelMapper.selectByPrimaryKey(dto.getGh());
		if(jcxxJzgjbxx == null){
			return result;
		}
		BeanUtil.copyProperties(jcxxJzgjbxx, result);
		result.setIdCardNo(jcxxJzgjbxx.getSfzjh());
		return result;
	}
    /** 
     * @see com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService#listByGhV1(com.xcwlkj.resourcecenter.model.dto.teacher.ListByGhV1DTO)
     */
	@Override
	public ListByGhV1VO listByGhV1(ListByGhV1DTO dto) {

		ListByGhV1VO result = new ListByGhV1VO();

		Example example = new Example(JcxxJzgjbxx.class);
		example.createCriteria()
				.andIn("gh", dto.getGhList());

		List<JcxxJzgjbxx> jcxxJzgjbxxes = modelMapper.selectByExample(example);
		if (jcxxJzgjbxxes.size() == 0){
			return result;
		}

		List<String> dwhs = jcxxJzgjbxxes.stream().map(JcxxJzgjbxx::getDwh).distinct().collect(Collectors.toList());
		Map<String, String> dwmcMap = jcxxYxsdwjbxxService.getDwmcMap(dwhs);

		result.setTeacherList(new ArrayList<>());
		for (JcxxJzgjbxx jcxxJzgjbxx : jcxxJzgjbxxes) {
			SysTeacherItemVO sysTeacherItemVO = new SysTeacherItemVO();
			sysTeacherItemVO.setDwh(jcxxJzgjbxx.getDwh());
			sysTeacherItemVO.setDwmc(dwmcMap.getOrDefault(jcxxJzgjbxx.getDwh(), ""));
			sysTeacherItemVO.setGh(jcxxJzgjbxx.getGh());
			sysTeacherItemVO.setXm(jcxxJzgjbxx.getXm());
			sysTeacherItemVO.setEjxkm(jcxxJzgjbxx.getEjxkm());
			sysTeacherItemVO.setXbm(jcxxJzgjbxx.getXbm());
			result.getTeacherList().add(sysTeacherItemVO);
		}

		return result;
	}

	@Override
	public void batchInsert(List<JcxxJzgjbxx> jcxxJzgjbxxList) {
		for (JcxxJzgjbxx jcxxJzgjbxx : jcxxJzgjbxxList) {
			jcxxJzgjbxx.setCjsj(new Date());
			jcxxJzgjbxx.setXgsj(new Date());
		}
		modelMapper.batchInsert(jcxxJzgjbxxList);
	}

	@Override
	public void batchInsertOrUpdate(List<JcxxJzgjbxx> jcxxJzgjbxxList) {
		modelMapper.batchInsertOrUpdate(jcxxJzgjbxxList);
	}

	@Override
	public int deleteByExample(Example example) {
		return modelMapper.deleteByExample(example);
	}

	@Override
	public InfoByGhVO infoByGh(InfoByGhDTO dto) {
		JcxxJzgjbxx jcxxJzgjbxx = modelMapper.selectByPrimaryKey(dto.getGh());
		InfoByGhVO infoByGhVO = new InfoByGhVO();
		infoByGhVO.setGh(jcxxJzgjbxx.getGh());
		infoByGhVO.setXm(jcxxJzgjbxx.getXm());
		infoByGhVO.setXbm(jcxxJzgjbxx.getXbm());
		return infoByGhVO;
	}

	@Override
	public void teacherImportPhotos(MultipartFile multipartFile) throws IOException {
		//保存zip压缩包至本地
		File tempFolders = new File("temp/teacher/zp");
		if(!tempFolders.exists()){
			tempFolders.mkdirs();
		}
		File tempFile = File.createTempFile("original", ".zip",
				tempFolders);
		FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), tempFile);
		//解压
		String now = String.valueOf(System.currentTimeMillis());
		File unzipTempFolders = new File("temp/teacher/unzip/"+now);
		if(!unzipTempFolders.exists()){
			unzipTempFolders.mkdirs();
		}
		ZipUtils.unzipFile(tempFile.getAbsolutePath(),"temp/teacher/unzip/"+now);
		//获取解压后的文件
		File unzip = new File("temp/teacher/unzip/"+now);
		File[] fs = unzip.listFiles();
		for (File f : fs) {
			String name = f.getName();
			UploadVO uploadVO = XcDfsClient.uploadStream(f.toString(), 0, "private");
			String gh = name.substring(0, name.indexOf("."));
			Example example = new Example(JcxxJzgjbxx.class);
			Example.Criteria createCriteria = example.createCriteria();
			createCriteria.andEqualTo("gh", gh);
			JcxxJzgjbxx jcxxJzgjbxx = new JcxxJzgjbxx();
			jcxxJzgjbxx.setXgsj(DateUtil.getCurrentDT());
			jcxxJzgjbxx.setZp(uploadVO.getList().get(0).getFilePath());
			modelMapper.updateByExampleSelective(jcxxJzgjbxx, example);
		}
		//删除临时文件
		tempFile.delete();
		//删除临时文件夹
		FileUtils.deleteDirectory(unzipTempFolders);
	}
}