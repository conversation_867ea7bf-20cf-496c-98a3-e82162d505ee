package com.xcwlkj.resourcecenter.service.impl;


import com.xcwlkj.resourcecenter.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

public class BaseServiceImpl< M extends Mapper<T>, T> implements BaseService<T> {

	@Autowired
	protected M baseMapper;

	@Override
	public int insert(T entity) {
		return baseMapper.insert(entity);
	}

	@Override
	public int updateByPrimaryKey(T entity) {
		return baseMapper.updateByPrimaryKey(entity);
	}

	@Override
	public List<T> selectByExample(Example example) {
		return baseMapper.selectByExample(example);
	}

	@Override
	public T selectByPrimaryKey(String key) {
		return baseMapper.selectByPrimaryKey(key);
	}

	@Override
	public int updateByPrimaryKeySelective(T entity) {
		return baseMapper.updateByPrimaryKeySelective(entity);
	}
    
    @Override
    public List<T> selectAll() {
        return baseMapper.selectAll();
    }
    
    @Override
	public T selectOneByExample(Example example) {
		return baseMapper.selectOneByExample(example);
	}

	@Override
	public int deleteByExample(Example example) {
		
		return baseMapper.deleteByExample(example);
	}

	@Override
	public int updateByExampleSelective(T entity, Example example) {
		return baseMapper.updateByExampleSelective(entity, example);
	}

	@Override
	public int deleteByPrimaryKey(String key) {
		return baseMapper.deleteByPrimaryKey(key);
	}

	@Override
	public int insertSelective(T entity) {
		try {
			Class<?> aClass = entity.getClass();
			Method setCjsj = aClass.getMethod("setCjsj", Date.class);
			setCjsj.invoke(entity, new Date());
		} catch (InvocationTargetException | NoSuchMethodException | IllegalAccessException ignored) {
		}

		try {
			Class<?> aClass = entity.getClass();
			Method setXgsj = aClass.getMethod("setXgsj", Date.class);
			setXgsj.invoke(entity, new Date());
		} catch (InvocationTargetException | NoSuchMethodException | IllegalAccessException ignored) {
		}
		return baseMapper.insertSelective(entity);
	}
	
}
