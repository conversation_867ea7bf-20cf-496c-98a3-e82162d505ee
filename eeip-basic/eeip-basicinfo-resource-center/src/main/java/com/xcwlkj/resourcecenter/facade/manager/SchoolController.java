/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.facade.manager;

import com.xcwlkj.base.BaseController;
import com.xcwlkj.resourcecenter.model.dto.school.ConfigureV1DTO;
import com.xcwlkj.resourcecenter.model.dto.school.DetailsV1DTO;
import com.xcwlkj.resourcecenter.model.req.school.ConfigureV1ReqModel;
import com.xcwlkj.resourcecenter.model.req.school.DetailsV1ReqModel;
import com.xcwlkj.resourcecenter.model.resp.school.ConfigureV1RespModel;
import com.xcwlkj.resourcecenter.model.resp.school.DetailsV1RespModel;
import com.xcwlkj.resourcecenter.model.vo.school.DetailsV1VO;
import com.xcwlkj.resourcecenter.service.JcxxXxjbxxService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * School控制层
 * <AUTHOR>
 * @version $Id: SchoolController.java, v 0.1 2022年09月28日 16时56分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("SchoolManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SchoolController extends BaseController {

    @Resource
    private JcxxXxjbxxService jcxxXxjbxxService;
	
   /**
    * 学校配置
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/resourcecenter/v1/school/configure")
    public Wrapper<ConfigureV1RespModel> configureV1(@Validated @RequestBody ConfigureV1ReqModel reqModel) {
		log.info("收到请求开始：[学校配置][/manager/resourcecenter/v1/school/configure]reqModel:"+reqModel.toString());
		ConfigureV1DTO dto = new ConfigureV1DTO();
        dto.setXxdm(reqModel.getXxdm());
        dto.setXxmc(reqModel.getXxmc());
        dto.setXxywmc(reqModel.getXxywmc());
        dto.setXxdz(reqModel.getXxdz());
        dto.setXxyzbm(reqModel.getXxyzbm());
        dto.setXzqhm(reqModel.getXzqhm());
        dto.setJxny(reqModel.getJxny());
        dto.setXxbxlxm(reqModel.getXxbxlxm());
        dto.setXxjbzm(reqModel.getXxjbzm());
        dto.setXxzgbmm(reqModel.getXxzgbmm());
        dto.setZzjgm(reqModel.getZzjgm());
        dto.setXzgh(reqModel.getXzgh());
        dto.setXzxm(reqModel.getXzxm());
        dto.setDwfzrh(reqModel.getDwfzrh());
        dto.setLxdh(reqModel.getLxdh());
        dto.setCzdh(reqModel.getCzdh());
        dto.setDzxx(reqModel.getDzxx());
        dto.setMode(reqModel.getMode());
        dto.setLogo(reqModel.getLogo());
        jcxxXxjbxxService.configureV1(dto);
        ConfigureV1RespModel respModel = new ConfigureV1RespModel();

		log.info("处理请求结束：[学校配置][/manager/resourcecenter/v1/school/configure]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }
   /**
    * 学校详情
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/resourcecenter/v1/school/details")
    public Wrapper<DetailsV1RespModel> detailsV1(@Validated @RequestBody DetailsV1ReqModel reqModel) {
		log.info("收到请求开始：[学校详情][/manager/resourcecenter/v1/school/details]reqModel:"+reqModel.toString());
		DetailsV1DTO dto = new DetailsV1DTO();
        dto.setXxdm(reqModel.getXxdm());
        DetailsV1VO result = jcxxXxjbxxService.detailsV1(dto);
        DetailsV1RespModel respModel = new DetailsV1RespModel();
        respModel.setXxdm(result.getXxdm());
        respModel.setXxmc(result.getXxmc());
        respModel.setXxywmc(result.getXxywmc());
        respModel.setXxdz(result.getXxdz());
        respModel.setXxyzbm(result.getXxyzbm());
        respModel.setXzqhm(result.getXzqhm());
        respModel.setJxny(result.getJxny());
        respModel.setXxbxlxm(result.getXxbxlxm());
        respModel.setXxjbzm(result.getXxjbzm());
        respModel.setXxzgbmm(result.getXxzgbmm());
        respModel.setZzjgm(result.getZzjgm());
        respModel.setXzgh(result.getXzgh());
        respModel.setXzxm(result.getXzxm());
        respModel.setDwfzrh(result.getDwfzrh());
        respModel.setLxdh(result.getLxdh());
        respModel.setCzdh(result.getCzdh());
        respModel.setDzxx(result.getDzxx());
        respModel.setMode(result.getMode());
        respModel.setLogo(result.getLogo());
		log.info("处理请求结束：[学校详情][/manager/resourcecenter/v1/school/details]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }

}