/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.sturegister.model.dto.adminclass.*;
import com.xcwlkj.sturegister.model.req.adminclass.*;
import com.xcwlkj.sturegister.model.resp.adminclass.*;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassDetailVO;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassListVO;
import com.xcwlkj.sturegister.service.JcxxBjService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * AdminClass控制层
 *
 * <AUTHOR>
 * @version $Id: AdminClassController.java, v 0.1 2022年09月29日 13时22分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("AdminClassManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class AdminClassController extends BaseController {

    @Resource
    private JcxxBjService jcxxBjService;

    /**
     * 详情班级
     *
     * @param reqModel
     * @return
     */
	@Permission("class:detail")
    @PostMapping(value = "/manager/sturegister/v1/class/detail")
    public Wrapper<ClassDetailRespModel> classDetail(@Validated @RequestBody ClassDetailReqModel reqModel) {
        log.info("收到请求开始：[详情班级][/manager/sturegister/v1/class/detail]reqModel:" + reqModel.toString());
        ClassDetailDTO dto = new ClassDetailDTO();
        dto.setBah(reqModel.getBah());
        ClassDetailVO result = jcxxBjService.classDetail(dto);
        ClassDetailRespModel respModel = new ClassDetailRespModel();
        BeanUtil.copyProperties(result, respModel);
        log.info("处理请求结束：[详情班级][/manager/sturegister/v1/class/detail]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 列表班级
     *
     * @param reqModel
     * @return
     */
	@Permission("class:list")
    @PostMapping(value = "/manager/sturegister/v1/class/list")
    public Wrapper<ClassListRespModel> classList(@Validated @RequestBody ClassListReqModel reqModel) {
        log.info("收到请求开始：[列表班级][/manager/sturegister/v1/class/list]reqModel:" + reqModel.toString());
        ClassListDTO dto = new ClassListDTO();
        dto.setBjmc(reqModel.getBjmc());
        dto.setSsnj(reqModel.getSsnj());
        dto.setDwh(reqModel.getDwh());
        dto.setZydm(reqModel.getZydm());
        dto.setBzrmc(reqModel.getBzrmc());
        dto.setIsBind(reqModel.getIsBind());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        ClassListVO result = jcxxBjService.classList(dto);
        ClassListRespModel respModel = new ClassListRespModel();
        respModel.setClassList(result.getClassList());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[列表班级][/manager/sturegister/v1/class/list]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 删除班级
     *
     * @param reqModel
     * @return
     */
	@Permission("class:delete")
    @PostMapping(value = "/manager/sturegister/v1/class/delete")
    public Wrapper<ClassDeleteRespModel> classDelete(@Validated @RequestBody ClassDeleteReqModel reqModel) {
        log.info("收到请求开始：[删除班级][/manager/sturegister/v1/class/delete]reqModel:" + reqModel.toString());
        ClassDeleteDTO dto = new ClassDeleteDTO();
        dto.setBhList(reqModel.getBhList());
        jcxxBjService.classDelete(dto);
        ClassDeleteRespModel respModel = new ClassDeleteRespModel();

        log.info("处理请求结束：[删除班级][/manager/sturegister/v1/class/delete]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 添加班级
     *
     * @param reqModel
     * @return
     */
	@Permission("class:add")
    @PostMapping(value = "/manager/sturegister/v1/class/add")
    public Wrapper<ClassAddRespModel> classAdd(@Validated @RequestBody ClassAddReqModel reqModel) {
        log.info("收到请求开始：[添加班级][/manager/sturegister/v1/class/add]reqModel:" + reqModel.toString());
        ClassAddDTO dto = new ClassAddDTO();
        dto.setBjmc(reqModel.getBjmc());
        dto.setZydm(reqModel.getZydm());
        dto.setZyfxdm(reqModel.getZyfxdm());
        dto.setJbny(reqModel.getJbny());
        dto.setSsnj(reqModel.getSsnj());
        dto.setBzrgh(reqModel.getBzrgh());
        dto.setBzxh(reqModel.getBzxh());
        dto.setFdyh(reqModel.getFdyh());
        dto.setSfddb(reqModel.getSfddb());
        dto.setBah(reqModel.getBah());
        dto.setDwh(reqModel.getDwh());
        dto.setJsh(reqModel.getJsh());
        dto.setJsmc(reqModel.getJsmc());
        dto.setAlias(reqModel.getAlias());
        dto.setSlogan(reqModel.getSlogan());
        jcxxBjService.classAdd(dto);
        ClassAddRespModel respModel = new ClassAddRespModel();

        log.info("处理请求结束：[添加班级][/manager/sturegister/v1/class/add]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 修改班级
     *
     * @param reqModel
     * @return
     */
	@Permission("class:update")
    @PostMapping(value = "/manager/sturegister/v1/class/update")
    public Wrapper<ClassUpdateRespModel> classUpdate(@Validated @RequestBody ClassUpdateReqModel reqModel) {
        log.info("收到请求开始：[修改班级][/manager/sturegister/v1/class/update]reqModel:" + reqModel.toString());
        ClassUpdateDTO dto = new ClassUpdateDTO();
        dto.setBjmc(reqModel.getBjmc());
        dto.setZydm(reqModel.getZydm());
        dto.setZyfxdm(reqModel.getZyfxdm());
        dto.setJbny(reqModel.getJbny());
        dto.setSsnj(reqModel.getSsnj());
        dto.setBzrgh(reqModel.getBzrgh());
        dto.setBzxh(reqModel.getBzxh());
        dto.setFdyh(reqModel.getFdyh());
        dto.setSfddb(reqModel.getSfddb());
        dto.setDwh(reqModel.getDwh());
        dto.setBah(reqModel.getBah());
        dto.setJsh(reqModel.getJsh());
        dto.setJsmc(reqModel.getJsmc());
        dto.setAlias(reqModel.getAlias());
        dto.setSlogan(reqModel.getSlogan());
        jcxxBjService.classUpdate(dto);
        ClassUpdateRespModel respModel = new ClassUpdateRespModel();

        log.info("处理请求结束：[修改班级][/manager/sturegister/v1/class/update]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

}