/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.biz.core.model.InfoPubBodyParams;
import com.xcwlkj.biz.core.model.enums.InfoPubActionType;
import com.xcwlkj.resourcecenter.model.domain.JcxxJzgjbxx;
import com.xcwlkj.resourcecenter.service.JcxxJzgjbxxService;
import com.xcwlkj.sturegister.mapper.BjExtraMapper;
import com.xcwlkj.sturegister.mapper.JcxxBjMapper;
import com.xcwlkj.sturegister.model.domain.BjExtra;
import com.xcwlkj.sturegister.model.domain.JcxxBj;
import com.xcwlkj.sturegister.model.domain.JcxxXnxq;
import com.xcwlkj.sturegister.model.dto.adminclass.*;
import com.xcwlkj.sturegister.model.dto.bj.ClasslistDTO;
import com.xcwlkj.sturegister.model.dto.electiveclass.*;
import com.xcwlkj.sturegister.model.enums.ClassEnum;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassDetailVO;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassItemVO;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassListVO;
import com.xcwlkj.sturegister.model.vo.bj.BjItemVO;
import com.xcwlkj.sturegister.model.vo.bj.ClasslistVO;
import com.xcwlkj.sturegister.model.vo.electiveclass.ElectiveDetailVO;
import com.xcwlkj.sturegister.model.vo.electiveclass.ElectiveListVO;
import com.xcwlkj.sturegister.service.JcxxBjService;
import com.xcwlkj.sturegister.util.SbxxUtils;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * jcxx_bj服务
 *
 * <AUTHOR>
 * @version $Id: JcxxBjServiceImpl.java, v 0.1 2022年09月27日 14时36分 xcwlkj.com Exp $
 */
@Service("jcxxBjService")
public class JcxxBjServiceImpl extends BaseServiceImpl<JcxxBjMapper, JcxxBj>  implements JcxxBjService {
    private static final Logger log = LoggerFactory.getLogger(JcxxBjServiceImpl.class);

    @Resource
    private JcxxBjMapper modelMapper;
    @Resource
    private BjExtraMapper bjExtraMapper;
    @Autowired
    private SbxxUtils sbxxUtils;
    @Resource
    private JcxxStuBoardOperateService jcxxStuBoardOperateService;
    @Resource
    private JcxxJzgjbxxService jcxxJzgjbxxService;


    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classDetail(com.xcwlkj.sturegister.model.dto.adminclass.ClassDetailDTO)
     */
    @Override
    public ClassDetailVO classDetail(ClassDetailDTO dto) {
        return modelMapper.classDetail(dto.getBah());
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classList(com.xcwlkj.sturegister.model.dto.adminclass.ClassListDTO)
     */
    @Override
    public ClassListVO classList(ClassListDTO dto) {
        ClassListVO result = new ClassListVO();
        
        // 如果有班主任名称条件，先查询匹配的工号
        if (StringUtils.isNotBlank(dto.getBzrmc())) {
            try {
                // 查询所有匹配名称的教师工号
                Example example = new Example(JcxxJzgjbxx.class);
                example.createCriteria().andLike("xm", "%" + dto.getBzrmc() + "%");
                List<JcxxJzgjbxx> teachers = jcxxJzgjbxxService.selectByExample(example);
                
                // 如果没有匹配的教师，直接返回空结果
                if (teachers == null || teachers.isEmpty()) {
                    result.setClassList(new ArrayList<>());
                    result.setTotalRows(0);
                    return result;
                }
                
                // 提取工号列表
                List<String> ghList = teachers.stream()
                        .map(JcxxJzgjbxx::getGh)
                        .collect(Collectors.toList());
                
                // 将工号列表传递给Mapper
                Page<ClassItemVO> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
                List<ClassItemVO> classItemVOS = modelMapper.classListByTeacherIds(dto, ClassEnum.XZB.getCode(), ghList);
                
                // 补充班主任名称显示
                populateTeacherNames(classItemVOS);
                
                result.setClassList(classItemVOS);
                result.setTotalRows((int) page.getTotal());
                return result;
            } catch (Exception e) {
                log.error("查询失败");// 发生异常时使用普通方式查询
            }
        }
        
        // 没有班主任名称条件或前面查询出错时，使用原始方式查询
        Page<ClassItemVO> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ClassItemVO> classItemVOS = modelMapper.classList(dto, ClassEnum.XZB.getCode());
        
        // 补充班主任名称显示
        populateTeacherNames(classItemVOS);
        
        result.setClassList(classItemVOS);
        result.setTotalRows((int) page.getTotal());
        return result;
    }
    
    /**
     * 批量填充班级列表的班主任名称
     * 
     * @param classList 班级列表
     */
    private void populateTeacherNames(List<ClassItemVO> classList) {
        if (classList == null || classList.isEmpty()) {
            return;
        }
        
        // 提取所有班级的班主任工号
        Set<String> allTeacherGhs = new HashSet<>();
        for (ClassItemVO vo : classList) {
            if (StringUtils.isNotBlank(vo.getBzrgh())) {
                String[] ghArray = vo.getBzrgh().split(",");
                for (String gh : ghArray) {
                    if (StringUtils.isNotBlank(gh)) {
                        allTeacherGhs.add(gh.trim());
                    }
                }
            }
        }
        
        // 批量查询所有教师的名称
        Map<String, String> teacherNameMap = new HashMap<>();
        if (!allTeacherGhs.isEmpty()) {
            try {
                Example example = new Example(JcxxJzgjbxx.class);
                example.createCriteria().andIn("gh", new ArrayList<>(allTeacherGhs));
                List<JcxxJzgjbxx> teachers = jcxxJzgjbxxService.selectByExample(example);
                
                if (teachers != null) {
                    for (JcxxJzgjbxx teacher : teachers) {
                        teacherNameMap.put(teacher.getGh(), teacher.getXm());
                    }
                }
            } catch (Exception e) {
                log.error("批量查询教师信息失败", e);
            }
        }
        
        // 设置班级的班主任名称
        for (ClassItemVO vo : classList) {
            if (StringUtils.isNotBlank(vo.getBzrgh())) {
                String[] ghArray = vo.getBzrgh().split(",");
                List<String> teacherNames = new ArrayList<>();
                
                for (String gh : ghArray) {
                    if (StringUtils.isNotBlank(gh)) {
                        String teacherName = teacherNameMap.get(gh.trim());
                        if (StringUtils.isNotBlank(teacherName)) {
                            teacherNames.add(teacherName);
                        }
                    }
                }
                
                if (!teacherNames.isEmpty()) {
                    vo.setBzrmc(String.join("、", teacherNames));
                }
            }
        }
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classDelete(com.xcwlkj.sturegister.model.dto.adminclass.ClassDeleteDTO)
     */
    @Override
    public void classDelete(ClassDeleteDTO dto) {
        Example example = new Example(JcxxBj.class);
        example.createCriteria()
                .andIn("bah", dto.getBhList()).andEqualTo("xxbbj", "0");

        Example emBjExtra = new Example(BjExtra.class);
        emBjExtra.createCriteria()
                .andIn("bah", dto.getBhList());
        bjExtraMapper.deleteByExample(emBjExtra);
        modelMapper.deleteByExample(example);
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classAdd(com.xcwlkj.sturegister.model.dto.adminclass.ClassAddDTO)
     */
    @Override
    public void classAdd(ClassAddDTO dto) {
        // 检查班级是否已存在
        JcxxBj existingBj = modelMapper.selectByPrimaryKey(dto.getBah());
        if (existingBj != null) {
            throw new BusinessException("班级编号[" + dto.getBah() + "]已存在，不能重复添加");
        }
        
        JcxxBj jcxxBj = new JcxxBj();
        BeanUtils.copyProperties(dto, jcxxBj);
        jcxxBj.setCjsj(new Date());
        jcxxBj.setXgsj(new Date());
        jcxxBj.setXxbbj(ClassEnum.XZB.getCode());
        modelMapper.insert(jcxxBj);

        //绑定教室
        if (StringUtils.isNotBlank(dto.getJsh())) {
            Example emBjEx = new Example(BjExtra.class);
            emBjEx.createCriteria()
                    .andNotEqualTo("bah", dto.getBah())
                    .andEqualTo("jsh", dto.getJsh());
            List<BjExtra> bjExtras = bjExtraMapper.selectByExample(emBjEx);
            if (!bjExtras.isEmpty()){
                throw new BusinessException("此教室已经被绑定到班级[" + bjExtras.get(0).getBah() + "]");
            }
        }
        BjExtra bjExtra = new BjExtra();
        BeanUtils.copyProperties(dto,bjExtra);
        bjExtraMapper.insert(bjExtra);

        if (StringUtils.isNotBlank(dto.getJsh())) {
            InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.pub.getValue(),
                    "classroom", IdGenerateUtil.generateId(), new JSONObject());
            jcxxStuBoardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(Arrays.asList(dto.getJsh()), "4869"), pubBodyParams);
        }
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classUpdate(com.xcwlkj.sturegister.model.dto.adminclass.ClassUpdateDTO)
     */
    @Override
    public void classUpdate(ClassUpdateDTO dto) {
        JcxxBj jcxxBj = new JcxxBj();
        BeanUtils.copyProperties(dto, jcxxBj);
        jcxxBj.setXgsj(new Date());
        modelMapper.updateByPrimaryKeySelective(jcxxBj);

        if (StringUtils.isNotBlank(dto.getJsh())) {
            Example emBjEx = new Example(BjExtra.class);
            emBjEx.createCriteria()
                    .andNotEqualTo("bah", dto.getBah())
                    .andEqualTo("jsh", dto.getJsh());
            List<BjExtra> bjExtras = bjExtraMapper.selectByExample(emBjEx);
            if (!bjExtras.isEmpty()){
                throw new BusinessException("此教室已经被绑定到班级[" + bjExtras.get(0).getBah() + "]");
            }
        }
        BjExtra bjExtra = bjExtraMapper.selectByPrimaryKey(dto.getBah());
        if (bjExtra != null){
            //修改绑定教室
            BjExtra bj = new BjExtra();
            BeanUtils.copyProperties(dto,bj);
            bjExtraMapper.updateByPrimaryKey(bj);
        } else {
            if (StringUtils.isNotBlank(dto.getJsh()) ||
                    StringUtils.isNotBlank(dto.getAlias()) ||
                    StringUtils.isNotBlank(dto.getSlogan())){
                //添加绑定教室信息
                BjExtra bj = new BjExtra();
                BeanUtils.copyProperties(dto,bj);
                bjExtraMapper.insert(bj);
            }
        }

        if (StringUtils.isNotBlank(dto.getJsh())) {
            InfoPubBodyParams pubBodyParams = InfoPubBodyParams.of(InfoPubActionType.pub.getValue(),
                    "classroom", IdGenerateUtil.generateId(), new JSONObject());
            jcxxStuBoardOperateService.infoPublish(sbxxUtils.getDevicesSupplier(Arrays.asList(dto.getJsh()), "4869"), pubBodyParams);
        }
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#electiveAdd(com.xcwlkj.sturegister.model.dto.electiveclass.ElectiveAddDTO)
     */
    @Override
    public void electiveAdd(ElectiveAddDTO dto) {
        JcxxBj jcxxBj = new JcxxBj();
        BeanUtils.copyProperties(dto, jcxxBj);
        jcxxBj.setCjsj(new Date());
        jcxxBj.setXgsj(new Date());
        jcxxBj.setXxbbj(ClassEnum.XXB.getCode());
        modelMapper.insert(jcxxBj);
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#electiveUpdate(com.xcwlkj.sturegister.model.dto.electiveclass.ElectiveUpdateDTO)
     */
    @Override
    public void electiveUpdate(ElectiveUpdateDTO dto) {
        JcxxBj jcxxBj = new JcxxBj();
        BeanUtils.copyProperties(dto, jcxxBj);
        jcxxBj.setXgsj(new Date());
        modelMapper.updateByPrimaryKeySelective(jcxxBj);
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#electiveList(com.xcwlkj.sturegister.model.dto.electiveclass.ElectiveListDTO)
     */
    @Override
    public ElectiveListVO electiveList(ElectiveListDTO dto) {
        ElectiveListVO result = new ElectiveListVO();

        ClassListDTO classListDTO = new ClassListDTO();
        BeanUtil.copyProperties(dto, classListDTO);
        Page<JcxxXnxq> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        List<ClassItemVO> classItemVOS = modelMapper.classList(classListDTO, ClassEnum.XXB.getCode());

        result.setClassList(classItemVOS);
        result.setTotalRows((int) page.getTotal());
        return result;
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#electiveDetail(com.xcwlkj.sturegister.model.dto.electiveclass.ElectiveDetailDTO)
     */
    @Override
    public ElectiveDetailVO electiveDetail(ElectiveDetailDTO dto) {
        JcxxBj jcxxBj = modelMapper.selectByPrimaryKey(dto.getBah());
        ElectiveDetailVO result = new ElectiveDetailVO();
        BeanUtils.copyProperties(jcxxBj, result);
        result.setCjsj(DateUtil.format(jcxxBj.getCjsj(), "yyyy-MM-dd HH:mm:ss"));
        result.setXgsj(DateUtil.format(jcxxBj.getXgsj(), "yyyy-MM-dd HH:mm:ss"));
        return result;
    }

    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#electiveDelete(com.xcwlkj.sturegister.model.dto.electiveclass.ElectiveDeleteDTO)
     */
    @Override
    public void electiveDelete(ElectiveDeleteDTO dto) {
        Example example = new Example(JcxxBj.class);
        example.createCriteria().andIn("bah", dto.getBhList()).andEqualTo("xxbbj", "1");
        modelMapper.deleteByExample(example);
    }


    /**
     * @see com.xcwlkj.sturegister.service.JcxxBjService#classlist(com.xcwlkj.sturegister.model.dto.bj.ClasslistDTO)
     */
    @Override
    public ClasslistVO classlist(ClasslistDTO dto) {
        ClasslistVO result = new ClasslistVO();
        Example example = new Example(JcxxBj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xxbbj", dto.getXxbbj());
        if (StringUtils.isNotBlank(dto.getBjmc())) {
            criteria.andLike("bjmc", "%" + dto.getBjmc() + "%");
        }
        if (StringUtils.isNotBlank(dto.getSsnj())) {
            criteria.andEqualTo("ssnj", dto.getSsnj());
        }
        if (StringUtils.isNotBlank(dto.getDwh())) {
            criteria.andEqualTo("dwh", dto.getDwh());
        }
        Page<JcxxXnxq> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        List<JcxxBj> jcxxBjList = modelMapper.selectByExample(example);

        List<BjItemVO> collect = jcxxBjList.stream().map(jcxxBj -> {
            BjItemVO item = new BjItemVO();
            BeanUtils.copyProperties(jcxxBj, item);
            return item;
        }).collect(Collectors.toList());

        result.setBjList(collect);
        result.setTotalRows((int) page.getTotal());
        return result;
    }

    @Override
    public void batchInsert(List<JcxxBj> jcxxBjList) {
        for (JcxxBj jcxxBj : jcxxBjList) {
            jcxxBj.setCjsj(new Date());
            jcxxBj.setXgsj(new Date());
        }
        modelMapper.batchInsert(jcxxBjList);
    }

    @Override
    public int deleteByExample(Example example) {
        return modelMapper.deleteByExample(example);
    }

    @Override
    public JcxxBj isBZR(String userName) {
        Example example = new Example(JcxxBj.class);
        example.createCriteria().andEqualTo("bzrgh", userName);
        List<JcxxBj> jcxxBjs = modelMapper.selectByExample(example);
        if (jcxxBjs.size() > 0) {
            return jcxxBjs.get(0);
        }
        return null;
    }

    /**
     * 获取当前用户管理的所有班级
     * @param userName 用户名
     * @return 班级列表
     */
    @Override
    public List<JcxxBj> getBZRClasses(String userName) {
        Example example = new Example(JcxxBj.class);
        example.createCriteria()
            .andLike("bzrgh", "%" + userName + "%");  // 使用模糊查询匹配逗号分隔的班主任工号
        return modelMapper.selectByExample(example);
    }
}