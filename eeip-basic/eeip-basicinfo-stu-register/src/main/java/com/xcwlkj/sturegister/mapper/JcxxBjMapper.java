/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.sturegister.mapper;

import java.util.List;

import com.xcwlkj.sturegister.model.dto.adminclass.ClassListDTO;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassDetailVO;
import com.xcwlkj.sturegister.model.vo.adminclass.ClassItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.sturegister.model.domain.JcxxBj;


/**
 * jcxx_bj数据库操作
 *
 * <AUTHOR>
 * @version $Id: InitJcxxBjMapper.java, v 0.1 2022年09月27日 14时36分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JcxxBjMapper extends MyMapper<JcxxBj> {

    /**
     * 分页查询jcxx_bj
     *
     * @param example
     * @return
     */
    List<JcxxBj> pageList(JcxxBj example);


    List<ClassItemVO> classList(@Param("dto") ClassListDTO dto, @Param("xxbbj")String xxbbj);
    
    /**
     * 根据教师工号列表查询班级
     * 
     * @param dto 查询条件
     * @param xxbbj 班级类型
     * @param teacherIds 教师工号列表
     * @return 班级列表
     */
    List<ClassItemVO> classListByTeacherIds(@Param("dto") ClassListDTO dto, @Param("xxbbj") String xxbbj, @Param("teacherIds") List<String> teacherIds);

    /**
     * 班级详情
     * @param bah 班号
     * @return
     */
    ClassDetailVO classDetail(@Param("bah") String bah);

    /**
     * 批量插入
     */
    void batchInsert(@Param("jcxxBjList") List<JcxxBj> jcxxBjList);
}
