<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.sturegister.mapper.JcxxBjMapper">
    <resultMap id="BaseResultMap" type="com.xcwlkj.sturegister.model.domain.JcxxBj">
        <id column="bah" jdbcType="VARCHAR" property="bah"/>
        <result column="bjmc" jdbcType="VARCHAR" property="bjmc"/>
        <result column="zydm" jdbcType="VARCHAR" property="zydm"/>
        <result column="zyfxdm" jdbcType="VARCHAR" property="zyfxdm"/>
        <result column="jbny" jdbcType="VARCHAR" property="jbny"/>
        <result column="ssnj" jdbcType="VARCHAR" property="ssnj"/>
        <result column="bzrgh" jdbcType="VARCHAR" property="bzrgh"/>
        <result column="bzxh" jdbcType="VARCHAR" property="bzxh"/>
        <result column="fdyh" jdbcType="VARCHAR" property="fdyh"/>
        <result column="sfddb" jdbcType="VARCHAR" property="sfddb"/>
        <result column="dwh" jdbcType="VARCHAR" property="dwh"/>
        <result column="xxbbj" jdbcType="VARCHAR" property="xxbbj"/>
        <result column="cjsj" jdbcType="TIMESTAMP" property="cjsj"/>
        <result column="xgsj" jdbcType="TIMESTAMP" property="xgsj"/>

    </resultMap>
    <!-- 列信息 -->
    <sql id="Base_Column_List">
        bah
        ,
        bjmc,
        zydm,
        zyfxdm,
        jbny,
        ssnj,
        bzrgh,
        bzxh,
        fdyh,
        sfddb,
        dwh,
        xxbbj,
        cjsj,
        xgsj

    </sql>

    <!-- where条件 -->
    <sql id="Base_Where_Condition">
        <if test="bah != null and bah != ''">
            AND bah = #{bah,jdbcType=VARCHAR}
        </if>
        <if test="bjmc != null and bjmc != ''">
            AND bjmc = #{bjmc,jdbcType=VARCHAR}
        </if>
        <if test="zydm != null and zydm != ''">
            AND zydm = #{zydm,jdbcType=VARCHAR}
        </if>
        <if test="zyfxdm != null and zyfxdm != ''">
            AND zyfxdm = #{zyfxdm,jdbcType=VARCHAR}
        </if>
        <if test="jbny != null and jbny != ''">
            AND jbny = #{jbny,jdbcType=VARCHAR}
        </if>
        <if test="ssnj != null and ssnj != ''">
            AND ssnj = #{ssnj,jdbcType=VARCHAR}
        </if>
        <if test="bzrgh != null and bzrgh != ''">
            AND bzrgh = #{bzrgh,jdbcType=VARCHAR}
        </if>
        <if test="bzxh != null and bzxh != ''">
            AND bzxh = #{bzxh,jdbcType=VARCHAR}
        </if>
        <if test="fdyh != null and fdyh != ''">
            AND fdyh = #{fdyh,jdbcType=VARCHAR}
        </if>
        <if test="sfddb != null and sfddb != ''">
            AND sfddb = #{sfddb,jdbcType=VARCHAR}
        </if>
        <if test="dwh != null and dwh != ''">
            AND dwh = #{dwh,jdbcType=VARCHAR}
        </if>
        <if test="xxbbj != null and xxbbj != ''">
            AND xxbbj = #{xxbbj,jdbcType=VARCHAR}
        </if>
        <if test="cjsj != null and cjsj != ''">
            AND cjsj = #{cjsj,jdbcType=TIMESTAMP}
        </if>
        <if test="xgsj != null and xgsj != ''">
            AND xgsj = #{xgsj,jdbcType=TIMESTAMP}
        </if>

    </sql>

    <!-- order by条件 -->
    <sql id="Base_OrderBy_Condition">
        <if test="orderBy != null and orderBy !=''">
            ORDER BY ${orderBy}
        </if>
    </sql>

    <!-- update条件 -->
    <sql id="Base_Set_Condition">
        <set>
            <if test="bah != null ">
                bah = #{bah,jdbcType=VARCHAR},
            </if>
            <if test="bjmc != null ">
                bjmc = #{bjmc,jdbcType=VARCHAR},
            </if>
            <if test="zydm != null ">
                zydm = #{zydm,jdbcType=VARCHAR},
            </if>
            <if test="zyfxdm != null ">
                zyfxdm = #{zyfxdm,jdbcType=VARCHAR},
            </if>
            <if test="jbny != null ">
                jbny = #{jbny,jdbcType=VARCHAR},
            </if>
            <if test="ssnj != null ">
                ssnj = #{ssnj,jdbcType=VARCHAR},
            </if>
            <if test="bzrgh != null ">
                bzrgh = #{bzrgh,jdbcType=VARCHAR},
            </if>
            <if test="bzxh != null ">
                bzxh = #{bzxh,jdbcType=VARCHAR},
            </if>
            <if test="fdyh != null ">
                fdyh = #{fdyh,jdbcType=VARCHAR},
            </if>
            <if test="sfddb != null ">
                sfddb = #{sfddb,jdbcType=VARCHAR},
            </if>
            <if test="dwh != null ">
                dwh = #{dwh,jdbcType=VARCHAR},
            </if>
            <if test="xxbbj != null ">
                xxbbj = #{xxbbj,jdbcType=VARCHAR},
            </if>
            <if test="cjsj != null ">
                cjsj = #{cjsj,jdbcType=TIMESTAMP},
            </if>
            <if test="xgsj != null ">
                xgsj = #{xgsj,jdbcType=TIMESTAMP}
            </if>

        </set>
    </sql>

    <!-- 分页查询 -->
    <select id="pageList" parameterType="com.xcwlkj.sturegister.model.domain.JcxxBj"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jcxx_bj
        where 1=1
        <include refid="Base_Where_Condition"/>
        <include refid="Base_OrderBy_Condition"/>
    </select>

    <select id="classList" resultType="com.xcwlkj.sturegister.model.vo.adminclass.ClassItemVO">
        select
            bj.bjmc,
            bj.jbny,
            bj.ssnj,
            bj.sfddb,
            bj.dwh,
            bj.bah,
            bj.bzrgh,
            zy.zymc,
            dw.dwmc
        from jcxx_bj bj 
        left join jcxx_zyxx zy on bj.zydm=zy.zyh
        left join jcxx_yxsdwjbxx dw on bj.dwh=dw.dwh
        left join bj_extra extra on extra.bah = bj.bah
        <where>
            <if test="dto.isBind == 0">
                (extra.jsh is null or extra.jsh = '')
            </if>
            <if test="dto.isBind == 1">
                (extra.jsh is not null and extra.jsh != '')
            </if>
            <if test="xxbbj != null and xxbbj != ''">
                and bj.xxbbj = #{xxbbj}
            </if>
            <if test="dto.bjmc != null and dto.bjmc != ''">
                and bj.bjmc like concat('%', #{dto.bjmc}, '%')
            </if>
            <if test="dto.ssnj != null and dto.ssnj != ''">
                and bj.ssnj = #{dto.ssnj}
            </if>
            <if test="dto.dwh != null and dto.dwh != ''">
                and bj.dwh = #{dto.dwh}
            </if>
            <if test="dto.zydm != null and dto.zydm != ''">
                and bj.zydm = #{dto.zydm}
            </if>
            <!-- 班主任名称过滤已移至Java层处理 -->
            <if test="dto.bzrmc != null and dto.bzrmc != ''">
                <!-- 此条件已在应用层处理，此处作为兼容保留但不生效 -->
            </if>
        </where>
    </select>

    <select id="classListByTeacherIds" resultType="com.xcwlkj.sturegister.model.vo.adminclass.ClassItemVO">
        select
            bj.bjmc,
            bj.jbny,
            bj.ssnj,
            bj.sfddb,
            bj.dwh,
            bj.bah,
            bj.bzrgh,
            zy.zymc,
            dw.dwmc
        from jcxx_bj bj 
        left join jcxx_zyxx zy on bj.zydm=zy.zyh
        left join jcxx_yxsdwjbxx dw on bj.dwh=dw.dwh
        left join bj_extra extra on extra.bah = bj.bah
        <where>
            <if test="dto.isBind == 0">
                (extra.jsh is null or extra.jsh = '')
            </if>
            <if test="dto.isBind == 1">
                (extra.jsh is not null and extra.jsh != '')
            </if>
            <if test="xxbbj != null and xxbbj != ''">
                and bj.xxbbj = #{xxbbj}
            </if>
            <if test="dto.bjmc != null and dto.bjmc != ''">
                and bj.bjmc like concat('%', #{dto.bjmc}, '%')
            </if>
            <if test="dto.ssnj != null and dto.ssnj != ''">
                and bj.ssnj = #{dto.ssnj}
            </if>
            <if test="dto.dwh != null and dto.dwh != ''">
                and bj.dwh = #{dto.dwh}
            </if>
            <if test="dto.zydm != null and dto.zydm != ''">
                and bj.zydm = #{dto.zydm}
            </if>
            <!-- 根据教师工号列表过滤 -->
            <if test="teacherIds != null and teacherIds.size() > 0">
                and (
                    <foreach collection="teacherIds" item="gh" separator=" or ">
                        FIND_IN_SET(#{gh}, bj.bzrgh)
                    </foreach>
                )
            </if>
        </where>
    </select>

    <select id="classDetail" resultType="com.xcwlkj.sturegister.model.vo.adminclass.ClassDetailVO">
        select
            bj.bjmc,
            bj.bah,
            bj.zydm,
            zy.zymc,
            bj.zyfxdm,
            zy.xkmlm,
            bj.jbny,
            bj.ssnj,
            bj.bzrgh,
            bj.bzxh,
            bj.fdyh,
            bj.sfddb,
            bj.dwh,
            dw.dwmc,
            extra.jsh,
            extra.jsmc,
            extra.alias,
            extra.slogan
        from jcxx_bj bj left join jcxx_zyxx zy on bj.zydm = zy.zyh
            left join jcxx_yxsdwjbxx dw on bj.dwh = dw.dwh
            left join bj_extra extra on extra.bah = bj.bah
        <where>
            bj.bah = #{bah}
        </where>
    </select>


    <!--批量插入-->
    <select id="batchInsert" parameterType="com.xcwlkj.sturegister.model.domain.JcxxBj">
        insert into jcxx_bj (bah,bjmc,zydm,zyfxdm,jbny,ssnj,bzrgh,bzxh,fdyh,
        sfddb,dwh,xxbbj,cjsj,xgsj)
        values
        <foreach collection="jcxxBjList" item="item" separator="," >
            (#{item.bah},#{item.bjmc},#{item.zydm},#{item.zyfxdm},#{item.jbny}
            ,#{item.ssnj},#{item.bzrgh},#{item.bzxh},#{item.fdyh},#{item.sfddb}
            ,#{item.dwh},#{item.xxbbj},#{item.cjsj},#{item.xgsj})
        </foreach>
    </select>
</mapper>
