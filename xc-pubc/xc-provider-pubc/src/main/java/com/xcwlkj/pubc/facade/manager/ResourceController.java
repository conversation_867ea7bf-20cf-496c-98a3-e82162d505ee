package com.xcwlkj.pubc.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.console.model.req.ssoAuth.*;
import com.xcwlkj.console.model.resp.ssoAuth.*;
import com.xcwlkj.console.model.vo.ssoAuth.*;
import com.xcwlkj.pubc.service.ResourceService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @version $Id: RoleController.java, v 0.1 2018年11月05日 下午05:02:44 xcwlkj.com Exp $
 */
@RestController
@RequestMapping(value = "/manager/pubc", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ResourceController extends BaseController {

	@Autowired
	private ResourceService resourceService;
	
	/**
     * 查询当前的角色的所有资源内容
     * @return
     */
    @PostMapping(value = "/resource/queryCurrtRoleOfResource")
    Wrapper<QueryCurrtRoleOfResourceRespModel> queryCurrtRoleOfResource(@RequestBody QueryCurrtRoleOfResourceReqModel reqModel) {
        QueryCurrtRoleOfResourceRespModel respModel = new QueryCurrtRoleOfResourceRespModel();
        List<RoleResourceRelationVO> result = resourceService
            .queryCurrtRoleOfResource(reqModel.getRoleId());
        respModel.setRoleResourceRelationVOList(result);

        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 查找资源列表
     * @param reqModel
     * @return
     */
	@Permission("resource:queryListWithPage")
    @PostMapping(value = "/resource/queryListWithPage")
    public Wrapper<QueryResourcerListWithPageRespModel> queryResourcerListWithPage(@RequestBody QueryResourcerListWithPageReqModel reqModel) {
        QueryResourcerListWithPageRespModel respModel = new QueryResourcerListWithPageRespModel();
        QueryResourcerListWithPageVO result = resourceService.queryListWithPage(reqModel);
        respModel.setResourceVOList(result.getResourceVOList());
        respModel.setTotalRows(result.getTotalRows());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 新增/修改资源表
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/save")
    public Wrapper<SaveResourceRespModel> saveResource(@RequestBody SaveResourceReqModel reqModel) {
        SaveResourceRespModel respModel = new SaveResourceRespModel();
        resourceService.saveResource(reqModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 根据id删除资源表
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/deleteResourceById")
    public Wrapper<DeleteResourceByIdRespModel> deleteResourceById(@RequestBody DeleteResourceByIdReqModel reqModel) {
        DeleteResourceByIdRespModel respModel = new DeleteResourceByIdRespModel();
        resourceService.deleteResourceById(reqModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 根据id查询资源表信息
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/getResourceById")
    public Wrapper<GetResourceByIdRespModel> getResourceById(@RequestBody GetResourceByIdReqModel reqModel) {
        GetResourceByIdRespModel respModel = new GetResourceByIdRespModel();
        GetResourceByIdVO result = resourceService.getResourceById(reqModel);
        BeanUtils.copyProperties(result, respModel);
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 查询所有资源信息
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/queryResource")
    public Wrapper<QueryResourceRespModel> queryResource(@RequestBody QueryResourceReqModel reqModel) {
        QueryResourceRespModel respModel = new QueryResourceRespModel();
        QueryResourceVO result = resourceService.queryResource(reqModel);
        respModel.setResourceVOList(result.getResourceVOList());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 根据用户的授权角色集合查询资源信息
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/findResourcesByAuthorityRoles")
    public Wrapper<FindResourcesByAuthorityRolesRespModel> findResourcesByAuthorityRoles(@RequestBody FindResourcesByAuthorityRolesReqModel reqModel) {
        FindResourcesByAuthorityRolesRespModel respModel = new FindResourcesByAuthorityRolesRespModel();
        FindResourcesByAuthorityRolesVO result = resourceService.findResourcesByAuthorityRoles(reqModel);
        respModel.setResourceVOS(result.getResourceVOS());
        return WrapMapper.ok(reqModel, respModel);
    }


    /**
     * 资源树形结构
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/resourceTreeData")
    public Wrapper<ResourceTreeDataRespModel> resourceTreeData(@RequestBody ResourceTreeDataReqModel reqModel) {
        ResourceTreeDataRespModel respModel = new ResourceTreeDataRespModel();
        ResourceTreeDataVO result = resourceService.resourceTreeData(reqModel);
        respModel.setResourceTreeVOList(result.getResourceTreeVOList());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 根据角色获取资源
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/roleMenuTreeData")
    public Wrapper<RoleMenuTreeDataRespModel> roleMenuTreeData(@RequestBody RoleMenuTreeDataReqModel reqModel) {
        RoleMenuTreeDataRespModel respModel = new RoleMenuTreeDataRespModel();
        RoleMenuTreeDataVO result = resourceService.roleMenuTreeData(reqModel);
        respModel.setResourceTreeVOList(result.getResourceTreeVOList());
        return WrapMapper.ok(reqModel, respModel);
    }


    /**
     * 新增角色获取所有资源树
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/resource/roleAllMenuTreeData")
    public Wrapper<RoleAllMenuTreeDataRespModel> roleAllMenuTreeData(@RequestBody RoleAllMenuTreeDataReqModel reqModel) {
        RoleAllMenuTreeDataRespModel respModel = new RoleAllMenuTreeDataRespModel();
        RoleAllMenuTreeDataVO result = resourceService.roleAllMenuTreeData(reqModel);
        respModel.setResourceTreeVOList(result.getResourceTreeVOList());
        return WrapMapper.ok(reqModel, respModel);
    }

}
