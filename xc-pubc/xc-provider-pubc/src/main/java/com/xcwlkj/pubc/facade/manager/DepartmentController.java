/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.console.model.dto.department.ModifyDepartmentDTO;
import com.xcwlkj.console.model.dto.department.SaveDepartmentDTO;
import com.xcwlkj.console.model.req.department.AddDepartmentReqModel;
import com.xcwlkj.console.model.req.department.DeleteDepartmentReqModel;
import com.xcwlkj.console.model.req.department.ModifyDepartmentReqModel;
import com.xcwlkj.console.model.req.ssoAuth.QueryCurrtUserOfDepartListReqModel;
import com.xcwlkj.console.model.req.ssoAuth.QueryDepartmentDetailByIdReqModel;
import com.xcwlkj.console.model.resp.department.AddDepartmentRespModel;
import com.xcwlkj.console.model.resp.department.DeleteDepartmentRespModel;
import com.xcwlkj.console.model.resp.department.ModifyDepartmentRespModel;
import com.xcwlkj.console.model.resp.ssoAuth.QueryCurrtUserOfDepartListRespModel;
import com.xcwlkj.console.model.resp.ssoAuth.QueryDepartmentDetailByIdRespModel;
import com.xcwlkj.console.model.vo.ssoAuth.DepartmentSummaryVO;
import com.xcwlkj.console.model.vo.ssoAuth.DepartmentVO;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.pubc.model.dto.department.SyncDepartmentDTO;
import com.xcwlkj.pubc.model.req.department.SyncDepartmentReqModel;
import com.xcwlkj.pubc.model.resp.department.SyncDepartmentRespModel;
import com.xcwlkj.pubc.service.DepartmentService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 * @version $Id: DepartmentController.java, v 0.1 2018年11月20日 下午02:26:25 xcwlkj.com Exp $
 */
@Slf4j
@RestController
@RequestMapping(value = "/manager/pubc", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class DepartmentController extends BaseController {

    @Resource
    private DepartmentService departmentService;
    /**
     * 查询当前用户的机构与子机构列表
     * 
     * @param reqModel
     * @return
     */
    @PostMapping("/user/queryCurrtUserOfDepartList")
    public Wrapper<QueryCurrtUserOfDepartListRespModel> queryCurrtUserOfDepartList(@RequestBody QueryCurrtUserOfDepartListReqModel reqModel) {

        XcSsoUser xcSsoUser = RequestUtil.getLoginUser();

        QueryCurrtUserOfDepartListRespModel respModel = new QueryCurrtUserOfDepartListRespModel();

        DepartmentSummaryVO result = departmentService
            .queryCurrtUserOfDepartList(xcSsoUser.getUserId());
        respModel.setDepartmentSummaryVO(result);

        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 新增部门
     * 
     * @param reqModel
     * @return
     */
	@Permission("auth:addDepartment")
    @PostMapping("/auth/addDepartment")
    public Wrapper<AddDepartmentRespModel> addDepartment(@RequestBody AddDepartmentReqModel reqModel) {

        AddDepartmentRespModel respModel = new AddDepartmentRespModel();
		SaveDepartmentDTO modelDTO = new SaveDepartmentDTO();
		BeanUtils.copyProperties(reqModel, modelDTO);

        departmentService.saveDepartment(modelDTO);
        return WrapMapper.ok(reqModel, respModel);
    }
	/**
     * 修改部门
     * 
     * @param reqModel
     * @return
     */
	@Permission("auth:modifyDepartment")
    @PostMapping("/auth/modifyDepartment")
    public Wrapper<ModifyDepartmentRespModel> modifyDepartment(@RequestBody ModifyDepartmentReqModel reqModel) {

    	ModifyDepartmentRespModel respModel = new ModifyDepartmentRespModel();
    	ModifyDepartmentDTO modelDTO = new ModifyDepartmentDTO();
    	BeanUtils.copyProperties(reqModel, modelDTO);

        departmentService.modifyDepartmentById(modelDTO);
        return WrapMapper.ok(reqModel, respModel);
    }	
	/**
     * 删除部门
     * 
     * @param reqModel
     * @return
     */
	@Permission("auth:deleteDepartment")
    @PostMapping("/auth/deleteDepartment")
    public Wrapper<DeleteDepartmentRespModel> deleteDepartment(@RequestBody DeleteDepartmentReqModel reqModel) {

    	DeleteDepartmentRespModel respModel = new DeleteDepartmentRespModel();
    	departmentService.deleteDepartmentById(reqModel.getId());
        return WrapMapper.ok(reqModel, respModel);
    }
	/**
     * 根据id查询一个部门
     * 
     * @param reqModel
     * @return
     */
	@Permission("auth:getDepartmentById")
    @PostMapping("/auth/getDepartmentById")
    public Wrapper<QueryDepartmentDetailByIdRespModel> getDepartmentById(@RequestBody QueryDepartmentDetailByIdReqModel reqModel) {
    	QueryDepartmentDetailByIdRespModel respModel = new QueryDepartmentDetailByIdRespModel();
        
        DepartmentVO vo = departmentService.getDepartmentById(reqModel.getId());
        
        DepartmentVO parentDepartment = departmentService.getDepartmentById(vo.getParentId());
        if(parentDepartment != null){
        	vo.setParentName(parentDepartment.getName());
        }
        respModel.setDepartmentVO(vo);
        return WrapMapper.ok(reqModel, respModel);
    }
       /**
    * 部门数据同步
    * @param reqModel
    * @return
    */
	@Permission("auth:syncDepartment")
    @PostMapping(value = "/auth/syncDepartment")
    public Wrapper<SyncDepartmentRespModel> syncDepartment(@RequestBody SyncDepartmentReqModel reqModel) {
		log.info("收到请求开始：[部门数据同步][/manager/pubc/auth/syncDepartment]reqModel:"+reqModel.toString());
		SyncDepartmentDTO dto = new SyncDepartmentDTO();

        departmentService.syncDepartment(dto);
        SyncDepartmentRespModel respModel = new SyncDepartmentRespModel();

		log.info("处理请求结束：[部门数据同步][/manager/pubc/auth/syncDepartment]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }

}