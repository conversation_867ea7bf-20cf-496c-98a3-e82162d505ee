2025-07-08 13:43:34.457  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1ce87225] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:35.022  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-08 13:43:37.194  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-08 13:43:37.197  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-08 13:43:37.201  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] ory$DuplicateJsonObjectContextCustomizer : 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class
	jar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

2025-07-08 13:43:37.209  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : The following profiles are active: alone
2025-07-08 13:43:44.394  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-08 13:43:44.398  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-08 13:43:44.722  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 307ms. Found 0 Redis repository interfaces.
2025-07-08 13:43:44.847  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-08 13:43:45.120  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-08 13:43:45.883  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e
2025-07-08 13:43:46.911  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-08 13:43:46.920  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-08 13:43:46.936  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-08 13:43:47.070  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$5dc64ebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.071  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$16f893ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.249  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$f692074d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.598  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$dc714ca5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.707  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ce6f28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.761  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.794  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$18927e4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.875  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:47.952  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$6bc02a57] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:48.034  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1ce87225] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 13:43:48.794  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 13:43:48.814  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-08 13:43:48.815  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-08 13:43:48.815  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-08 13:43:48.815  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-08 13:43:50.808  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-08 13:43:55.085 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-08 13:43:55.095  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 13:43:57.267  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-08 13:43:57.277  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-08 13:43:58.478  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-08 13:43:58.718 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@a96a299],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@523ddffa],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@76b5c127],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@6a95fa85],]
2025-07-08 13:43:58.718 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-08 13:44:14.304  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-08 13:44:14.312  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-08 13:44:16.188  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-08 13:44:18.171  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-08 13:44:18.493 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-08 13:44:18.517 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-08 13:44:18.536 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 9
2025-07-08 13:44:18.558  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-08 13:44:18.559  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-08 13:44:18.560  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-08 13:44:18.560  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-08 13:44:18.561  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-08 13:44:18.562  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-08 13:44:18.562  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-08 13:44:18.563  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-08 13:44:18.563  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-08 13:44:18.567  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-08 13:44:23.484  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 13:44:23.556  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 13:44:25.020  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-08 13:44:25.020  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-08 13:44:25.020  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-08 13:44:25.249  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-08 13:44:29.266  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 13:44:31.865  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-08 13:44:33.234  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 13:44:33.234  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-08 13:44:33.235  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-08 13:44:33.235  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-08 13:44:33.235  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-08 13:44:33.669  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-08 13:44:33.669  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-08 13:44:33.744  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : Started JyCommonPlatformServiceImplTest in 60.982 seconds (JVM running for 62.135)
2025-07-08 13:44:33.758  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-08 13:44:33.759  WARN [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider\eeip-standalone/config/application-alone.yml
2025-07-08 13:44:33.763  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-08 13:44:33.815 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-08 13:44:33.848 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-08 13:44:33.849 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-08 13:44:33.867 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 13:44:33.868 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-08 13:44:33.869 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-08 13:44:34.552 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 294
2025-07-08 13:44:34.556  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-08 13:44:34.556  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-08 13:44:34.556  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service
2025-07-08 13:44:34.572 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-08 13:44:34.890  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-08 13:44:34.894  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 13:44:34.894  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-08 13:44:34.895  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 13:44:34.896  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 13:44:34.924  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-08 13:44:35.749  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-08 13:44:35.749  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-08 13:44:35.753 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-08 13:44:35.753 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-08 13:44:35.761 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-08 13:44:35.764 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-08 13:44:35.764 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:35.775 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-08 13:44:36.488 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-08 13:44:36.489 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:36.496 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-08 13:44:36.650 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-08 13:44:36.651 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:36.667 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-08 13:44:38.155 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-08 13:44:38.155 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.163 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 13:44:38.204 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-08 13:44:38.205 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.212 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-08 13:44:38.277 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-08 13:44:38.277 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.285 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 13:44:38.333 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-08 13:44:38.333 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.342 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-08 13:44:38.589 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-08 13:44:38.589 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.597 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-08 13:44:38.651 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-08 13:44:38.652 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.659 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-08 13:44:38.758 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-08 13:44:38.758 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 13:44:38.765 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-08 13:44:38.795  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-08 13:44:38.795  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-08 13:44:38.795  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 13:44:38.796  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-08 13:44:38.796  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-08 13:44:38.804 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-08 13:44:38.805 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-08 13:44:38.798(Timestamp), 1(Integer)
2025-07-08 13:44:38.819 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 13:44:38.819  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-08 13:44:38.819  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-08 13:44:38.820  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-08 13:44:38.882 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.883 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-08 13:44:38.890 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.891 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.891 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-08 13:44:38.898 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.898 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.898 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-08 13:44:38.905 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.906 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.906 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-08 13:44:38.913 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.914 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.914 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-08 13:44:38.920 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.921 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.921 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-08 13:44:38.927 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.928 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.928 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-08 13:44:38.934 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.934 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.936 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-08 13:44:38.942 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.942 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:38.943 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-08 13:44:38.949 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:38.950 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 13:44:38.950 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-08 13:44:38.962 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-08 13:44:38.962 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 13:44:38.962 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-08 13:44:38.974 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-08 13:44:38.975 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 13:44:38.975 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-08 13:44:38.987 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-08 13:44:38.987 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-08 13:44:38.987 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-08 13:44:39.001 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-08 13:44:39.002 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 13:44:39.003 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-08 13:44:39.015 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-08 13:44:39.016 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 13:44:39.016 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-08 13:44:39.028 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-08 13:44:39.029 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 13:44:39.029 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-08 13:44:39.043 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-08 13:44:39.044 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 13:44:39.044 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-08 13:44:39.063 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-08 13:44:39.066 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-08 13:44:39.066 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-08 13:44:39.075 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-08 13:44:39.076 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.076 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-08 13:44:39.083 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.084 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.085 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-08 13:44:39.092 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.092 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.093 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-08 13:44:39.098 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.100 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.100 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-08 13:44:39.107 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.108 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-08 13:44:39.108 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-08 13:44:39.120 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-08 13:44:39.121 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.121 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-08 13:44:39.128 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.131 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-08 13:44:39.131 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-08 13:44:39.147 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-08 13:44:39.148  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-08 13:44:39.148 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 13:44:39.149 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-08 13:44:39.155 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 13:44:39.155  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-08 13:44:39.155  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-08 13:44:39.155  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 13:44:39.540 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.J.insertSelective                : ==>  Preparing: INSERT INTO jy_common_platform ( id,plat_type,server_ip,server_port,proxy_ip,proxy_port,app_id,app_key,create_time,update_time,file_server_url,file_server_channel,document_service,file_service_app_id,file_service_app_secret ) VALUES( ?,?,?,?,?,?,?,?,?,?,?,?,?,?,? ) 
2025-07-08 13:44:39.541 DEBUG [,,,] [eeip-standalone-service,,,,] 27456 --- [           main] c.x.i.m.J.insertSelective                : ==> Parameters: 25070813443902254467588471945216(String), HISOME(String), ***********00(String), 8080(String), ***********(String), 8081(String), testAppId(String), testAppKey(String), 2025-07-08 13:44:39.537(Timestamp), 2025-07-08 13:44:39.537(Timestamp), http://***********00:8811(String), channel001(String), documentService001(String), fileAppId001(String), fileAppSecret001(String)
2025-07-08 13:44:39.643  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-08 13:44:39.648  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-08 13:44:43.690  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped inbound
2025-07-08 13:44:43.691  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] ProxyFactoryBean$MethodInvocationGateway : stopped mqttGateway
2025-07-08 13:44:43.691  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.i.gateway.GatewayProxyFactoryBean    : stopped mqttGateway
2025-07-08 13:44:43.691  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 13:44:43.691  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 0 subscriber(s).
2025-07-08 13:44:43.691  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped _org.springframework.integration.errorLogger
2025-07-08 13:44:46.792  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-07-08 13:44:46.793  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.s.c.ThreadPoolTaskScheduler          : Shutting down ExecutorService 'taskScheduler'
2025-07-08 13:44:46.794  INFO [,,,] [eeip-standalone-service,,,,] 27456 --- [      Thread-23] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService
2025-07-08 14:13:41.699  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$df7f0802] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:42.254  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-08 14:13:42.487  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: label not found
2025-07-08 14:13:42.491  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] ory$DuplicateJsonObjectContextCustomizer : 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class
	jar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

2025-07-08 14:13:42.499  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : The following profiles are active: alone
2025-07-08 14:13:47.584  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-08 14:13:47.587  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-08 14:13:47.854  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 254ms. Found 0 Redis repository interfaces.
2025-07-08 14:13:47.962  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-08 14:13:48.207  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-08 14:13:48.647  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e
2025-07-08 14:13:49.580  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-08 14:13:49.588  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-08 14:13:49.604  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-08 14:13:49.718  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$205ce49b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:49.719  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$d98f29cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:49.870  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b9289d2a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.144  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c3650505] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.214  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$9f07e282] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.290  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$db291428] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.344  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.405  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.438  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$2e56c034] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:50.500  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$df7f0802] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:13:51.134  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:13:51.286  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:13:51.544  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:13:51.564  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-08 14:13:51.564  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-08 14:13:51.564  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-08 14:13:51.565  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-08 14:13:53.141  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-08 14:13:56.511 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-08 14:13:56.519  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:13:58.314  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-08 14:13:58.327  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-08 14:13:59.189  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-08 14:13:59.344 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@57346fb4],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@4fcaa4a0],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@12ef80b3],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@110598e4],]
2025-07-08 14:13:59.346 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-08 14:14:10.132  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-08 14:14:10.142  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-08 14:14:11.789  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-08 14:14:13.159  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-08 14:14:13.385 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-08 14:14:13.401 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-08 14:14:13.418 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 9
2025-07-08 14:14:13.436  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-08 14:14:13.436  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-08 14:14:13.437  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-08 14:14:13.437  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-08 14:14:13.438  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-08 14:14:13.438  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-08 14:14:13.438  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-08 14:14:13.438  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-08 14:14:13.439  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-08 14:14:13.441  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-08 14:14:17.404  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-08 14:14:17.404  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-08 14:14:17.404  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-08 14:14:17.564  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-08 14:14:20.892  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 14:14:23.605  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-08 14:14:24.993  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:14:24.994  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-08 14:14:24.994  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-08 14:14:24.995  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-08 14:14:25.420  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-08 14:14:25.420  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-08 14:14:25.497  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : Started JyCommonPlatformServiceImplTest in 45.387 seconds (JVM running for 46.32)
2025-07-08 14:14:25.517  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-08 14:14:25.518  WARN [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider\eeip-standalone/config/application-alone.yml
2025-07-08 14:14:25.524  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-08 14:14:25.576 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-08 14:14:25.613 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-08 14:14:25.613 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-08 14:14:25.628 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:14:25.629 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-08 14:14:25.629 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-08 14:14:25.642 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 294
2025-07-08 14:14:25.646  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-08 14:14:25.646  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-08 14:14:25.647  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service
2025-07-08 14:14:25.661 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-08 14:14:25.934  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:14:25.939  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:14:25.940  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:14:25.953  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-08 14:14:27.097  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-08 14:14:27.097  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-08 14:14:27.101 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-08 14:14:27.101 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-08 14:14:27.109 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-08 14:14:27.112 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-08 14:14:27.112 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:27.124 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-08 14:14:28.050 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-08 14:14:28.051 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:28.059 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-08 14:14:28.218 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-08 14:14:28.219 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:28.231 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-08 14:14:29.538 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-08 14:14:29.539 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:29.546 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:14:29.593 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-08 14:14:29.594 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:29.604 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-08 14:14:29.687 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-08 14:14:29.687 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:29.694 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:14:29.744 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-08 14:14:29.744 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:29.754 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-08 14:14:30.023 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-08 14:14:30.024 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:30.032 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-08 14:14:30.091 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-08 14:14:30.092 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:30.100 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-08 14:14:30.202 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-08 14:14:30.202 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:14:30.209 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-08 14:14:30.240  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-08 14:14:30.240  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-08 14:14:30.240  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:14:30.241  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-08 14:14:30.241  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-08 14:14:30.248 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-08 14:14:30.249 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-08 14:14:30.243(Timestamp), 1(Integer)
2025-07-08 14:14:30.304 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:14:30.304  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-08 14:14:30.304  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-08 14:14:30.304  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-08 14:14:30.372 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.372 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-08 14:14:30.383 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.384 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.385 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-08 14:14:30.392 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.392 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.392 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-08 14:14:30.399 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.399 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.400 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-08 14:14:30.406 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.408 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.408 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-08 14:14:30.414 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.416 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.416 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-08 14:14:30.422 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.423 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.423 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-08 14:14:30.431 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.431 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.431 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-08 14:14:30.439 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.439 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.440 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-08 14:14:30.448 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.448 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:14:30.448 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-08 14:14:30.463 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-08 14:14:30.463 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:14:30.464 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-08 14:14:30.476 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-08 14:14:30.478 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:14:30.478 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-08 14:14:30.491 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-08 14:14:30.492 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-08 14:14:30.492 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-08 14:14:30.506 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-08 14:14:30.507 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:14:30.508 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-08 14:14:30.521 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-08 14:14:30.522 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:14:30.522 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-08 14:14:30.535 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-08 14:14:30.535 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:14:30.536 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-08 14:14:30.549 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-08 14:14:30.550 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:14:30.550 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-08 14:14:30.564 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-08 14:14:30.566 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-08 14:14:30.566 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-08 14:14:30.578 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-08 14:14:30.579 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.579 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-08 14:14:30.587 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.588 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.588 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-08 14:14:30.595 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.596 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.596 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-08 14:14:30.602 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.603 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.603 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-08 14:14:30.610 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.610 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-08 14:14:30.611 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-08 14:14:30.629 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-08 14:14:30.629 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.629 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-08 14:14:30.637 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.638 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-08 14:14:30.639 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-08 14:14:30.657 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-08 14:14:30.657  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-08 14:14:30.658 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:14:30.658 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-08 14:14:30.665 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:14:30.666  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-08 14:14:30.666  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-08 14:14:30.666  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:14:30.859  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-11] c.xcwlkj.msgque.que.XcRocektMqConsumer   : 监听：JKYT_ATTENDANCE_JTXX,启动成功！
2025-07-08 14:14:31.009 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? 
2025-07-08 14:14:31.010 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==> Parameters: HISOME(String), *************(String), 7777(String), gxsfhy(String), 8efc68adb41744d1810d2b6527ed32c5(String), 2025-07-08 14:14:31.008(Timestamp), 24022010563201889096002716053504(String)
2025-07-08 14:14:31.031 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : <==    Updates: 1
2025-07-08 14:14:31.032 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? 
2025-07-08 14:14:31.032 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==> Parameters: http://*************:8812(String), HISOME_fileServerUrl(String)
2025-07-08 14:14:31.047 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : <==    Updates: 0
2025-07-08 14:14:31.048 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? 
2025-07-08 14:14:31.048 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==> Parameters: channel002(String), HISOME_fileServerChannel(String)
2025-07-08 14:14:31.069 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : <==    Updates: 0
2025-07-08 14:14:31.070 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? 
2025-07-08 14:14:31.071 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==> Parameters: documentService002(String), HISOME_documentService(String)
2025-07-08 14:14:31.089 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : <==    Updates: 0
2025-07-08 14:14:31.090 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? 
2025-07-08 14:14:31.090 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==> Parameters: fileAppId002(String), HISOME_fileServiceAppId(String)
2025-07-08 14:14:31.108 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : <==    Updates: 0
2025-07-08 14:14:31.108 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? 
2025-07-08 14:14:31.109 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : ==> Parameters: fileAppSecret002(String), HISOME_fileServiceAppSecret(String)
2025-07-08 14:14:31.125 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.updateTValueByTCode            : <==    Updates: 0
2025-07-08 14:14:31.126 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:14:31.126 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerUrl(String)
2025-07-08 14:14:31.133 DEBUG [,,,] [eeip-standalone-service,,,,] 49076 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:14:31.159  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-08 14:14:31.164  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-08 14:14:35.216  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped inbound
2025-07-08 14:14:35.216  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] ProxyFactoryBean$MethodInvocationGateway : stopped mqttGateway
2025-07-08 14:14:35.216  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.gateway.GatewayProxyFactoryBean    : stopped mqttGateway
2025-07-08 14:14:35.216  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 0 subscriber(s).
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped _org.springframework.integration.errorLogger
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.handler.serviceActivator
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
2025-07-08 14:14:35.217  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:14:36.247  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-07-08 14:14:36.248  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.s.c.ThreadPoolTaskScheduler          : Shutting down ExecutorService 'taskScheduler'
2025-07-08 14:14:36.249  INFO [,,,] [eeip-standalone-service,,,,] 49076 --- [      Thread-23] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService
2025-07-08 14:23:40.014  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5bdbcc99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:40.564  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-08 14:23:42.716  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-08 14:23:42.717  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-08 14:23:42.721  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] ory$DuplicateJsonObjectContextCustomizer : 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class
	jar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

2025-07-08 14:23:42.729  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : The following profiles are active: alone
2025-07-08 14:23:48.003  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-08 14:23:48.007  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-08 14:23:48.305  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 282ms. Found 0 Redis repository interfaces.
2025-07-08 14:23:48.466  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-08 14:23:48.756  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-08 14:23:49.274  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e
2025-07-08 14:23:50.224  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-08 14:23:50.231  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-08 14:23:50.244  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-08 14:23:50.362  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$9cb9a932] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:50.362  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$55ebee62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:50.518  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$358561c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:50.796  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3fc1c99c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:50.870  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$1b64a719] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:50.957  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$5785d8bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:51.024  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:51.092  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:51.136  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$aab384cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:51.202  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5bdbcc99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:23:51.815  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:23:51.949  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:23:52.244  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:23:52.272  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-08 14:23:52.273  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-08 14:23:52.273  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-08 14:23:52.274  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-08 14:23:53.952  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-08 14:23:56.697 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-08 14:23:56.704  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:23:58.473  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-08 14:23:58.487  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-08 14:23:59.411  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-08 14:23:59.584 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@e519afd],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@3799e7a8],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@5245b5e0],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@5ec1031d],]
2025-07-08 14:23:59.584 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-08 14:24:12.002  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-08 14:24:12.010  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-08 14:24:13.436  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-08 14:24:15.240  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-08 14:24:15.524 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-08 14:24:15.539 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-08 14:24:15.559 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 9
2025-07-08 14:24:15.577  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-08 14:24:15.578  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-08 14:24:15.579  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-08 14:24:15.579  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-08 14:24:15.579  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-08 14:24:15.580  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-08 14:24:15.580  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-08 14:24:15.581  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-08 14:24:15.582  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-08 14:24:15.584  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-08 14:24:20.226  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-08 14:24:20.226  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-08 14:24:20.226  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-08 14:24:20.405  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-08 14:24:23.664  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 14:24:26.315  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-08 14:24:27.744  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:24:27.746  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-08 14:24:27.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:24:27.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-08 14:24:27.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-08 14:24:28.202  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-08 14:24:28.202  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-08 14:24:28.297  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : Started JyCommonPlatformServiceImplTest in 49.721 seconds (JVM running for 50.798)
2025-07-08 14:24:28.317  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-08 14:24:28.317  WARN [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider\eeip-standalone/config/application-alone.yml
2025-07-08 14:24:28.322  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-08 14:24:28.376 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-08 14:24:28.416 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-08 14:24:28.416 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-08 14:24:28.431 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:24:28.433 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-08 14:24:28.433 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-08 14:24:28.448 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 294
2025-07-08 14:24:28.451  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-08 14:24:28.452  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-08 14:24:28.452  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service
2025-07-08 14:24:28.466 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-08 14:24:28.741  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-08 14:24:28.746  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-08 14:24:28.747  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:24:28.748  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:24:28.763  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-08 14:24:29.789  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-08 14:24:29.790  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-08 14:24:29.794 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-08 14:24:29.794 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-08 14:24:29.802 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-08 14:24:29.805 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-08 14:24:29.805 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:29.815 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-08 14:24:30.492 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-08 14:24:30.493 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:30.499 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-08 14:24:30.644 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-08 14:24:30.644 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:30.654 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-08 14:24:31.894 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-08 14:24:31.894 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:31.908 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:24:31.960 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-08 14:24:31.960 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:31.968 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-08 14:24:32.034 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-08 14:24:32.034 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:32.042 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:24:32.093 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-08 14:24:32.094 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:32.102 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-08 14:24:32.349 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-08 14:24:32.349 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:32.357 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-08 14:24:32.412 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-08 14:24:32.412 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:32.420 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-08 14:24:32.659 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-08 14:24:32.660 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:24:32.668 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-08 14:24:32.719  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-08 14:24:32.719  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-08 14:24:32.719  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:24:32.721  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-08 14:24:32.721  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-08 14:24:32.726 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-08 14:24:32.726 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-08 14:24:32.722(Timestamp), 1(Integer)
2025-07-08 14:24:32.740 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:24:32.741  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-08 14:24:32.741  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-08 14:24:32.741  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-08 14:24:32.808 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.809 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-08 14:24:32.817 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.818 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.819 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-08 14:24:32.826 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.827 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.827 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-08 14:24:32.834 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.835 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.835 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-08 14:24:32.843 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.844 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.845 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-08 14:24:32.852 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.853 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.853 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-08 14:24:32.859 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.860 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.860 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-08 14:24:32.867 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.867 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.868 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-08 14:24:32.875 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.876 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:32.876 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-08 14:24:32.883 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:32.883 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:24:32.884 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-08 14:24:32.896 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-08 14:24:32.897 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:24:32.897 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-08 14:24:32.934 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-08 14:24:32.935 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:24:32.936 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-08 14:24:32.949 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-08 14:24:32.949 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-08 14:24:32.950 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-08 14:24:32.963 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-08 14:24:32.964 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:24:32.964 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-08 14:24:32.977 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-08 14:24:32.977 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:24:32.978 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-08 14:24:32.991 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-08 14:24:32.991 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:24:32.991 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-08 14:24:33.004 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-08 14:24:33.004 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:24:33.004 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-08 14:24:33.018 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-08 14:24:33.020 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-08 14:24:33.021 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-08 14:24:33.030 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-08 14:24:33.031 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.031 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-08 14:24:33.041 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.042 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.042 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-08 14:24:33.049 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.050 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.050 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-08 14:24:33.056 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.057 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.057 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-08 14:24:33.064 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.065 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-08 14:24:33.065 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-08 14:24:33.079 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-08 14:24:33.080 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.080 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-08 14:24:33.087 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.089 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-08 14:24:33.089 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-08 14:24:33.108 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-08 14:24:33.108  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-08 14:24:33.108 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:24:33.109 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-08 14:24:33.118 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:24:33.118  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-08 14:24:33.118  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-08 14:24:33.119  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:24:33.454 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? 
2025-07-08 14:24:33.454 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==> Parameters: HISOME(String), *************(String), 7777(String), gxsfhy(String), 8efc68adb41744d1810d2b6527ed32c5(String), 2025-07-08 14:24:33.452(Timestamp), 24022010563201889096002716053504(String)
2025-07-08 14:24:33.470 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : <==    Updates: 1
2025-07-08 14:24:33.471 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.471 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerUrl(String)
2025-07-08 14:24:33.478 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:24:33.481 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) 
2025-07-08 14:24:33.482 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==> Parameters: 25070814243302254487670312957952(String), HISOME_fileServerUrl(String), 文件服务器地址(String), upperPlatDock(String), http://*************:8812(String), upperPlat(String), 2025-07-08 14:24:33.479(Timestamp), 2025-07-08 14:24:33.479(Timestamp)
2025-07-08 14:24:33.498 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : <==    Updates: 1
2025-07-08 14:24:33.499 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.500 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerChannel(String)
2025-07-08 14:24:33.507 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:24:33.508 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) 
2025-07-08 14:24:33.509 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==> Parameters: 25070814243302254487670547838976(String), HISOME_fileServerChannel(String), 文件服务渠道(String), upperPlatDock(String), channel002(String), upperPlat(String), 2025-07-08 14:24:33.507(Timestamp), 2025-07-08 14:24:33.507(Timestamp)
2025-07-08 14:24:33.524 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : <==    Updates: 1
2025-07-08 14:24:33.526 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.526 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_documentService(String)
2025-07-08 14:24:33.534 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:24:33.535 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) 
2025-07-08 14:24:33.535 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==> Parameters: 25070814243302254487670774331392(String), HISOME_documentService(String), 文档服务(String), upperPlatDock(String), documentService002(String), upperPlat(String), 2025-07-08 14:24:33.534(Timestamp), 2025-07-08 14:24:33.534(Timestamp)
2025-07-08 14:24:33.550 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : <==    Updates: 1
2025-07-08 14:24:33.551 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.552 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppId(String)
2025-07-08 14:24:33.559 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:24:33.560 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) 
2025-07-08 14:24:33.561 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==> Parameters: 25070814243302254487670992435200(String), HISOME_fileServiceAppId(String), 文件服务appId(String), upperPlatDock(String), fileAppId002(String), upperPlat(String), 2025-07-08 14:24:33.56(Timestamp), 2025-07-08 14:24:33.56(Timestamp)
2025-07-08 14:24:33.576 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : <==    Updates: 1
2025-07-08 14:24:33.576 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.577 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppSecret(String)
2025-07-08 14:24:33.585 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:24:33.585 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) 
2025-07-08 14:24:33.586 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : ==> Parameters: 25070814243302254487671202150400(String), HISOME_fileServiceAppSecret(String), 文件服务appSecret(String), upperPlatDock(String), fileAppSecret002(String), upperPlat(String), 2025-07-08 14:24:33.585(Timestamp), 2025-07-08 14:24:33.585(Timestamp)
2025-07-08 14:24:33.603 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.JySysDictMapper.insertSelective  : <==    Updates: 1
2025-07-08 14:24:33.604 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:24:33.605 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerUrl(String)
2025-07-08 14:24:33.612 DEBUG [,,,] [eeip-standalone-service,,,,] 30932 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-08 14:24:33.639  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-08 14:24:33.643  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-08 14:24:37.680  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped inbound
2025-07-08 14:24:37.680  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] ProxyFactoryBean$MethodInvocationGateway : stopped mqttGateway
2025-07-08 14:24:37.680  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.gateway.GatewayProxyFactoryBean    : stopped mqttGateway
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 0 subscriber(s).
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped _org.springframework.integration.errorLogger
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.handler.serviceActivator
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:24:37.682  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
2025-07-08 14:24:37.683  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:24:40.760  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-07-08 14:24:40.761  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.s.c.ThreadPoolTaskScheduler          : Shutting down ExecutorService 'taskScheduler'
2025-07-08 14:24:40.762  INFO [,,,] [eeip-standalone-service,,,,] 30932 --- [      Thread-23] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService
2025-07-08 14:31:09.574  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$65e76d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:10.121  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-08 14:31:10.361  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: label not found
2025-07-08 14:31:10.365  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] ory$DuplicateJsonObjectContextCustomizer : 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class
	jar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

2025-07-08 14:31:10.373  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : The following profiles are active: alone
2025-07-08 14:31:16.045  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-08 14:31:16.048  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-08 14:31:16.294  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 233ms. Found 0 Redis repository interfaces.
2025-07-08 14:31:16.414  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-08 14:31:16.671  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-08 14:31:17.132  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e
2025-07-08 14:31:18.064  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-08 14:31:18.070  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-08 14:31:18.084  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-08 14:31:18.193  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$a6c549a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.193  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$5ff78ed5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.362  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3f910234] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.636  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$49cd6a0f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.712  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$2570478c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.794  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$61917932] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.844  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.903  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.939  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$b4bf253e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:18.999  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$65e76d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-08 14:31:19.594  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:31:19.730  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:31:20.046  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:31:20.065  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-08 14:31:20.066  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-08 14:31:20.066  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-08 14:31:20.066  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-08 14:31:21.873  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-08 14:31:25.138 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-08 14:31:25.148  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:31:26.955  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-08 14:31:26.976  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-08 14:31:28.053  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-08 14:31:28.224 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@22f8a2c2],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@35c4686e],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@f6d381a],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@5ff839b0],]
2025-07-08 14:31:28.225 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-08 14:31:40.439  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-08 14:31:40.448  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-08 14:31:42.515  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-08 14:31:44.083  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-08 14:31:44.313 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-08 14:31:44.329 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-08 14:31:44.347 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 9
2025-07-08 14:31:44.366  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-08 14:31:44.366  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-08 14:31:44.366  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-08 14:31:44.367  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-08 14:31:44.367  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-08 14:31:44.367  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-08 14:31:44.368  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-08 14:31:44.368  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-08 14:31:44.369  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-08 14:31:44.372  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-08 14:31:48.514  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-08 14:31:48.514  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-08 14:31:48.514  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-08 14:31:48.681  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-08 14:31:52.076  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 14:31:54.706  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-08 14:31:56.121  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:31:56.121  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-08 14:31:56.121  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-08 14:31:56.122  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-08 14:31:56.566  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-08 14:31:56.567  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-08 14:31:56.664  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.s.i.JyCommonPlatformServiceImplTest  : Started JyCommonPlatformServiceImplTest in 48.607 seconds (JVM running for 49.521)
2025-07-08 14:31:56.685  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-08 14:31:56.686  WARN [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider\eeip-standalone/config/application-alone.yml
2025-07-08 14:31:56.693  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-08 14:31:56.749 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-08 14:31:56.788 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-08 14:31:56.788 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-08 14:31:56.802 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:31:56.803 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-08 14:31:56.803 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-08 14:31:56.818 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 294
2025-07-08 14:31:56.821  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-08 14:31:56.821  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-08 14:31:56.822  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service
2025-07-08 14:31:56.836 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-08 14:31:57.175  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-08 14:31:57.183  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:31:57.183  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-08 14:31:57.183  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-08 14:31:57.183  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:31:57.183  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-08 14:31:57.184  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-08 14:31:57.186  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:31:57.186  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:31:57.186  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-08 14:31:57.186  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-08 14:31:57.187  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-08 14:31:57.188  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-08 14:31:57.189  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:31:57.190  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-08 14:31:57.205  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-08 14:31:58.490  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-08 14:31:58.490  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-08 14:31:58.496 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-08 14:31:58.496 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-08 14:31:58.505 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-08 14:31:58.509 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-08 14:31:58.510 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:31:58.521 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-08 14:31:59.274 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-08 14:31:59.275 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:31:59.283 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-08 14:31:59.458 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-08 14:31:59.458 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:31:59.470 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-08 14:32:00.829 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-08 14:32:00.831 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:00.837 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:32:00.886 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-08 14:32:00.888 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:00.894 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-08 14:32:00.968 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-08 14:32:00.969 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:00.978 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-08 14:32:01.037 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-08 14:32:01.038 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:01.049 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-08 14:32:01.318 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-08 14:32:01.318 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:01.325 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-08 14:32:01.406 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-08 14:32:01.407 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:01.420 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-08 14:32:01.529 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-08 14:32:01.530 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-08 14:32:01.538 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-08 14:32:01.573  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-08 14:32:01.574  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-08 14:32:01.574  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-08 14:32:01.575  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-08 14:32:01.575  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-08 14:32:01.582 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-08 14:32:01.582 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-08 14:32:01.577(Timestamp), 1(Integer)
2025-07-08 14:32:01.596 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-08 14:32:01.596  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-08 14:32:01.596  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-08 14:32:01.596  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-08 14:32:01.674 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.676 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-08 14:32:01.683 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.685 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.686 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-08 14:32:01.694 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.694 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.694 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-08 14:32:01.701 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.702 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.702 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-08 14:32:01.709 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.711 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.711 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-08 14:32:01.718 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.719 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.719 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-08 14:32:01.728 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.729 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.729 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-08 14:32:01.737 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.737 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.738 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-08 14:32:01.745 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.745 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.745 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-08 14:32:01.752 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.753 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:32:01.754 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-08 14:32:01.767 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-08 14:32:01.767 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:32:01.768 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-08 14:32:01.780 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-08 14:32:01.781 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-08 14:32:01.781 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-08 14:32:01.795 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-08 14:32:01.796 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-08 14:32:01.796 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-08 14:32:01.810 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-08 14:32:01.811 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:32:01.811 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-08 14:32:01.824 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-08 14:32:01.825 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:32:01.826 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-08 14:32:01.838 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-08 14:32:01.839 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:32:01.839 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-08 14:32:01.852 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-08 14:32:01.852 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-08 14:32:01.853 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-08 14:32:01.866 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-08 14:32:01.868 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-08 14:32:01.869 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-08 14:32:01.878 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-08 14:32:01.879 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.879 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-08 14:32:01.886 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.887 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.888 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-08 14:32:01.895 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.895 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.895 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-08 14:32:01.903 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.903 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.904 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-08 14:32:01.910 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.911 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-08 14:32:01.911 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-08 14:32:01.924 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-08 14:32:01.924 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.924 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-08 14:32:01.933 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.935 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-08 14:32:01.935 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-08 14:32:01.952 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-08 14:32:01.952  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-08 14:32:01.953 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-08 14:32:01.953 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-08 14:32:01.959 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-08 14:32:01.960  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-08 14:32:01.960  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-08 14:32:01.960  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-08 14:32:02.307 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? 
2025-07-08 14:32:02.308 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : ==> Parameters: JSJF(String), *************(String), 8173(String), 123456(String), 123456(String), 2025-07-08 14:32:02.306(Timestamp), 23122810335601849946738119460864(String)
2025-07-08 14:32:02.326 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.updateByPrimaryKeySelective    : <==    Updates: 1
2025-07-08 14:32:02.327 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-08 14:32:02.327 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: JSJF_fileServerUrl(String)
2025-07-08 14:32:02.333 DEBUG [,,,] [eeip-standalone-service,,,,] 7668 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 0
2025-07-08 14:32:02.358  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-08 14:32:02.365  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-08 14:32:06.626  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped inbound
2025-07-08 14:32:06.626  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] ProxyFactoryBean$MethodInvocationGateway : stopped mqttGateway
2025-07-08 14:32:06.627  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.gateway.GatewayProxyFactoryBean    : stopped mqttGateway
2025-07-08 14:32:06.627  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-08 14:32:06.627  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 0 subscriber(s).
2025-07-08 14:32:06.627  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped _org.springframework.integration.errorLogger
2025-07-08 14:32:06.627  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-08 14:32:06.628  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
2025-07-08 14:32:06.628  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.handler.serviceActivator
2025-07-08 14:32:06.628  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-08 14:32:06.628  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
2025-07-08 14:32:06.628  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.mqttOutbound.serviceActivator
2025-07-08 14:32:07.661  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-07-08 14:32:07.662  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.s.c.ThreadPoolTaskScheduler          : Shutting down ExecutorService 'taskScheduler'
2025-07-08 14:32:07.663  INFO [,,,] [eeip-standalone-service,,,,] 7668 --- [      Thread-23] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService
