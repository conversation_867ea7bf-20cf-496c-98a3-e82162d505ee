{"@timestamp":"2025-07-08T05:43:34.457Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1ce87225] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:35.022Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T05:43:37.194Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-08T05:43:37.197Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-08T05:43:37.201Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-08T05:43:37.209Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-08T05:43:44.394Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T05:43:44.398Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T05:43:44.722Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 307ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T05:43:44.847Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T05:43:45.120Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T05:43:45.883Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e"}
{"@timestamp":"2025-07-08T05:43:46.911Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T05:43:46.920Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T05:43:46.936Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T05:43:47.070Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$5dc64ebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.071Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$16f893ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$f692074d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.598Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$dc714ca5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.707Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ce6f28] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.761Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.794Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$18927e4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.875Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:47.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$6bc02a57] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:48.034Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1ce87225] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T05:43:48.794Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T05:43:48.814Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-08T05:43:48.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-08T05:43:48.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-08T05:43:48.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-08T05:43:50.808Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T05:43:55.085Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-08T05:43:55.095Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T05:43:57.267Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-08T05:43:57.277Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-08T05:43:58.478Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T05:43:58.718Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@a96a299],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@523ddffa],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@76b5c127],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@6a95fa85],]"}
{"@timestamp":"2025-07-08T05:43:58.718Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-08T05:44:14.304Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T05:44:14.312Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T05:44:16.188Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-08T05:44:18.171Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-08T05:44:18.493Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-08T05:44:18.517Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:18.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 9"}
{"@timestamp":"2025-07-08T05:44:18.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-08T05:44:18.559Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-08T05:44:18.560Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-08T05:44:18.560Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-08T05:44:18.561Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-08T05:44:18.562Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-08T05:44:18.562Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-08T05:44:18.563Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-08T05:44:18.563Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-08T05:44:18.567Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-08T05:44:23.484Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T05:44:23.556Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T05:44:25.020Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-08T05:44:25.020Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-08T05:44:25.020Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-08T05:44:25.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-08T05:44:29.266Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-08T05:44:31.865Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-08T05:44:33.234Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T05:44:33.234Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T05:44:33.235Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T05:44:33.235Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T05:44:33.235Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T05:44:33.669Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-08T05:44:33.669Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-08T05:44:33.744Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"Started JyCommonPlatformServiceImplTest in 60.982 seconds (JVM running for 62.135)"}
{"@timestamp":"2025-07-08T05:44:33.758Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-08T05:44:33.759Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider\\eeip-standalone/config/application-alone.yml"}
{"@timestamp":"2025-07-08T05:44:33.763Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-08T05:44:33.815Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-08T05:44:33.848Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-08T05:44:33.849Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-08T05:44:33.867Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:33.868Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-08T05:44:33.869Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:34.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 294"}
{"@timestamp":"2025-07-08T05:44:34.556Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-08T05:44:34.556Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T05:44:34.556Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-08T05:44:34.572Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-08T05:44:34.890Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T05:44:34.894Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T05:44:34.894Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-08T05:44:34.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T05:44:34.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T05:44:34.924Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-08T05:44:35.749Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-08T05:44:35.749Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-08T05:44:35.753Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-08T05:44:35.753Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-08T05:44:35.761Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-08T05:44:35.764Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:35.764Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:35.775Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-08T05:44:36.488Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:36.489Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:36.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-08T05:44:36.650Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-08T05:44:36.651Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:36.667Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-08T05:44:38.155Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:38.155Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.163Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T05:44:38.204Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-08T05:44:38.205Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.212Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-08T05:44:38.277Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:38.277Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.285Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T05:44:38.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-08T05:44:38.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.342Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-08T05:44:38.589Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:38.589Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.597Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-08T05:44:38.651Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:38.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.659Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-08T05:44:38.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T05:44:38.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.765Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T05:44:38.795Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-08T05:44:38.795Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-08T05:44:38.795Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T05:44:38.796Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-08T05:44:38.796Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-08T05:44:38.804Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-08T05:44:38.805Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-08 13:44:38.798(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-08T05:44:38.819Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:38.819Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-08T05:44:38.819Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-08T05:44:38.820Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-08T05:44:38.882Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.883Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-08T05:44:38.890Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-08T05:44:38.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-08T05:44:38.905Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.906Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.906Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-08T05:44:38.913Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.914Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.914Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-08T05:44:38.920Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.921Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.921Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-08T05:44:38.927Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.928Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.928Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-08T05:44:38.934Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.934Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.936Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-08T05:44:38.942Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.942Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:38.943Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T05:44:38.949Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:38.950Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T05:44:38.950Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:38.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T05:44:38.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.974Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:38.975Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T05:44:38.975Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:38.987Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:38.987Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-08T05:44:38.987Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.001Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.002Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T05:44:39.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T05:44:39.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.028Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T05:44:39.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.043Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T05:44:39.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.066Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-08T05:44:39.066Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-08T05:44:39.075Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T05:44:39.076Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.076Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-08T05:44:39.083Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.084Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.085Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-08T05:44:39.092Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.092Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.093Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-08T05:44:39.098Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.100Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.100Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-08T05:44:39.107Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-08T05:44:39.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T05:44:39.120Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-08T05:44:39.128Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.131Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-08T05:44:39.131Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-08T05:44:39.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T05:44:39.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-08T05:44:39.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T05:44:39.149Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T05:44:39.155Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T05:44:39.155Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-08T05:44:39.155Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-08T05:44:39.155Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T05:44:39.540Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.J.insertSelective","rest":"==>  Preparing: INSERT INTO jy_common_platform ( id,plat_type,server_ip,server_port,proxy_ip,proxy_port,app_id,app_key,create_time,update_time,file_server_url,file_server_channel,document_service,file_service_app_id,file_service_app_secret ) VALUES( ?,?,?,?,?,?,?,?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T05:44:39.541Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"main","class":"c.x.i.m.J.insertSelective","rest":"==> Parameters: 25070813443902254467588471945216(String), HISOME(String), ***********00(String), 8080(String), ***********(String), 8081(String), testAppId(String), testAppKey(String), 2025-07-08 13:44:39.537(Timestamp), 2025-07-08 13:44:39.537(Timestamp), http://***********00:8811(String), channel001(String), documentService001(String), fileAppId001(String), fileAppSecret001(String)"}
{"@timestamp":"2025-07-08T05:44:39.643Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-08T05:44:39.648Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-08T05:44:43.690Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-08T05:44:43.691Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T05:44:43.691Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T05:44:43.691Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T05:44:43.691Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T05:44:43.691Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T05:44:46.792Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-08T05:44:46.793Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T05:44:46.794Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27456","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-08T06:13:41.699Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$df7f0802] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:42.254Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T06:13:42.487Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-08T06:13:42.491Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-08T06:13:42.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-08T06:13:47.584Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T06:13:47.587Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T06:13:47.854Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 254ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T06:13:47.962Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:13:48.207Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:13:48.647Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e"}
{"@timestamp":"2025-07-08T06:13:49.580Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T06:13:49.588Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T06:13:49.604Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T06:13:49.718Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$205ce49b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:49.719Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$d98f29cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:49.870Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b9289d2a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.144Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c3650505] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.214Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$9f07e282] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.290Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$db291428] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.344Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.405Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.438Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$2e56c034] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:50.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$df7f0802] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:13:51.134Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:13:51.286Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:13:51.544Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:13:51.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-08T06:13:51.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-08T06:13:51.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-08T06:13:51.565Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-08T06:13:53.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T06:13:56.511Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-08T06:13:56.519Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:13:58.314Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-08T06:13:58.327Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-08T06:13:59.189Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:13:59.344Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@57346fb4],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@4fcaa4a0],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@12ef80b3],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@110598e4],]"}
{"@timestamp":"2025-07-08T06:13:59.346Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-08T06:14:10.132Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T06:14:10.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T06:14:11.789Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-08T06:14:13.159Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-08T06:14:13.385Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-08T06:14:13.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:13.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 9"}
{"@timestamp":"2025-07-08T06:14:13.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-08T06:14:13.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-08T06:14:13.437Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-08T06:14:13.437Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-08T06:14:13.438Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-08T06:14:13.438Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-08T06:14:13.438Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-08T06:14:13.438Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-08T06:14:13.439Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-08T06:14:13.441Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-08T06:14:17.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-08T06:14:17.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-08T06:14:17.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-08T06:14:17.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-08T06:14:20.892Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-08T06:14:23.605Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-08T06:14:24.993Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:14:24.994Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:24.994Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:14:24.995Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:14:25.420Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-08T06:14:25.420Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-08T06:14:25.497Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"Started JyCommonPlatformServiceImplTest in 45.387 seconds (JVM running for 46.32)"}
{"@timestamp":"2025-07-08T06:14:25.517Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-08T06:14:25.518Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider\\eeip-standalone/config/application-alone.yml"}
{"@timestamp":"2025-07-08T06:14:25.524Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-08T06:14:25.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-08T06:14:25.613Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-08T06:14:25.613Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-08T06:14:25.628Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:25.629Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-08T06:14:25.629Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:25.642Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 294"}
{"@timestamp":"2025-07-08T06:14:25.646Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-08T06:14:25.646Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:14:25.647Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-08T06:14:25.661Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-08T06:14:25.934Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:14:25.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:14:25.940Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:14:25.953Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-08T06:14:27.097Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-08T06:14:27.097Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-08T06:14:27.101Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-08T06:14:27.101Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-08T06:14:27.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-08T06:14:27.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:27.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:27.124Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-08T06:14:28.050Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:28.051Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:28.059Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-08T06:14:28.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-08T06:14:28.219Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:28.231Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-08T06:14:29.538Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:29.539Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:29.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:14:29.593Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-08T06:14:29.594Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:29.604Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-08T06:14:29.687Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:29.687Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:29.694Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:14:29.744Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-08T06:14:29.744Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:29.754Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-08T06:14:30.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:30.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.032Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-08T06:14:30.091Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:30.092Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.100Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-08T06:14:30.202Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:14:30.202Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:14:30.240Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-08T06:14:30.240Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-08T06:14:30.240Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:14:30.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-08T06:14:30.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-08T06:14:30.248Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-08T06:14:30.249Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-08 14:14:30.243(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-08T06:14:30.304Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.304Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-08T06:14:30.304Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-08T06:14:30.304Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-08T06:14:30.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-08T06:14:30.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.384Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.385Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-08T06:14:30.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.392Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-08T06:14:30.399Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.399Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.400Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-08T06:14:30.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-08T06:14:30.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-08T06:14:30.422Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-08T06:14:30.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-08T06:14:30.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:14:30.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:14:30.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:14:30.464Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.476Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.478Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:14:30.478Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.491Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-08T06:14:30.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.507Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:14:30.508Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.521Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.522Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:14:30.522Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.535Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.535Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:14:30.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.549Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.550Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:14:30.550Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.564Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.566Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-08T06:14:30.566Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-08T06:14:30.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:14:30.579Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.579Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-08T06:14:30.587Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.588Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.588Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-08T06:14:30.595Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-08T06:14:30.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-08T06:14:30.610Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.610Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-08T06:14:30.611Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:14:30.629Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.629Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.629Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-08T06:14:30.637Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.638Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-08T06:14:30.639Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-08T06:14:30.657Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:30.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-08T06:14:30.658Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:14:30.658Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:14:30.665Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:14:30.666Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-08T06:14:30.666Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:14:30.666Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:14:30.859Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-11","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-08T06:14:31.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? "}
{"@timestamp":"2025-07-08T06:14:31.010Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==> Parameters: HISOME(String), *************(String), 7777(String), gxsfhy(String), 8efc68adb41744d1810d2b6527ed32c5(String), 2025-07-08 14:14:31.008(Timestamp), 24022010563201889096002716053504(String)"}
{"@timestamp":"2025-07-08T06:14:31.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:14:31.032Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? "}
{"@timestamp":"2025-07-08T06:14:31.032Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==> Parameters: http://*************:8812(String), HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-08T06:14:31.047Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:31.048Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? "}
{"@timestamp":"2025-07-08T06:14:31.048Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==> Parameters: channel002(String), HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-08T06:14:31.069Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:31.070Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? "}
{"@timestamp":"2025-07-08T06:14:31.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==> Parameters: documentService002(String), HISOME_documentService(String)"}
{"@timestamp":"2025-07-08T06:14:31.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:31.090Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? "}
{"@timestamp":"2025-07-08T06:14:31.090Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==> Parameters: fileAppId002(String), HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-08T06:14:31.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:31.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==>  Preparing: UPDATE jy_sys_dict SET t_value = ? WHERE t_code = ? "}
{"@timestamp":"2025-07-08T06:14:31.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"==> Parameters: fileAppSecret002(String), HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-08T06:14:31.125Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.updateTValueByTCode","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:14:31.126Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:14:31.126Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-08T06:14:31.133Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:14:31.159Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-08T06:14:31.164Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-08T06:14:35.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-08T06:14:35.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:14:35.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:14:35.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:14:35.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:14:36.247Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-08T06:14:36.248Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:14:36.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"49076","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-08T06:23:40.014Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5bdbcc99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:40.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T06:23:42.716Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-08T06:23:42.717Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-08T06:23:42.721Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-08T06:23:42.729Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-08T06:23:48.003Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T06:23:48.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T06:23:48.305Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 282ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T06:23:48.466Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:23:48.756Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:23:49.274Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e"}
{"@timestamp":"2025-07-08T06:23:50.224Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T06:23:50.231Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T06:23:50.244Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T06:23:50.362Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$9cb9a932] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:50.362Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$55ebee62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:50.518Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$358561c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:50.796Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3fc1c99c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:50.870Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$1b64a719] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:50.957Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$5785d8bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:51.024Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:51.092Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:51.136Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$aab384cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:51.202Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5bdbcc99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:23:51.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:23:51.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:23:52.244Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:23:52.272Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-08T06:23:52.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-08T06:23:52.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-08T06:23:52.274Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-08T06:23:53.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T06:23:56.697Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-08T06:23:56.704Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:23:58.473Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-08T06:23:58.487Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-08T06:23:59.411Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:23:59.584Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@e519afd],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@3799e7a8],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@5245b5e0],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@5ec1031d],]"}
{"@timestamp":"2025-07-08T06:23:59.584Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-08T06:24:12.002Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T06:24:12.010Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T06:24:13.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-08T06:24:15.240Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-08T06:24:15.524Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-08T06:24:15.539Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:15.559Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 9"}
{"@timestamp":"2025-07-08T06:24:15.577Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-08T06:24:15.578Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-08T06:24:15.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-08T06:24:15.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-08T06:24:15.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-08T06:24:15.580Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-08T06:24:15.580Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-08T06:24:15.581Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-08T06:24:15.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-08T06:24:15.584Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-08T06:24:20.226Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-08T06:24:20.226Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-08T06:24:20.226Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-08T06:24:20.405Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-08T06:24:23.664Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-08T06:24:26.315Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-08T06:24:27.744Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:24:27.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:27.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:24:27.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:24:27.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:24:28.202Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-08T06:24:28.202Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-08T06:24:28.297Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"Started JyCommonPlatformServiceImplTest in 49.721 seconds (JVM running for 50.798)"}
{"@timestamp":"2025-07-08T06:24:28.317Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-08T06:24:28.317Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider\\eeip-standalone/config/application-alone.yml"}
{"@timestamp":"2025-07-08T06:24:28.322Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-08T06:24:28.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-08T06:24:28.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-08T06:24:28.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-08T06:24:28.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:28.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-08T06:24:28.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:28.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 294"}
{"@timestamp":"2025-07-08T06:24:28.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-08T06:24:28.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:24:28.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-08T06:24:28.466Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-08T06:24:28.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:24:28.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:24:28.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:24:28.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:24:28.763Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-08T06:24:29.789Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-08T06:24:29.790Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-08T06:24:29.794Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:29.794Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-08T06:24:29.802Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-08T06:24:29.805Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:29.805Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:29.815Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-08T06:24:30.492Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:30.493Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:30.499Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-08T06:24:30.644Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-08T06:24:30.644Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:30.654Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-08T06:24:31.894Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:31.894Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:31.908Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:24:31.960Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-08T06:24:31.960Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:31.968Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-08T06:24:32.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:32.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.042Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:24:32.093Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-08T06:24:32.094Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.102Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-08T06:24:32.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:32.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-08T06:24:32.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:32.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-08T06:24:32.659Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:24:32.660Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.668Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:24:32.719Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-08T06:24:32.719Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-08T06:24:32.719Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:24:32.721Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-08T06:24:32.721Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-08T06:24:32.726Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:32.726Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-08 14:24:32.722(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-08T06:24:32.740Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-08T06:24:32.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-08T06:24:32.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-08T06:24:32.808Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.809Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-08T06:24:32.817Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.818Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.819Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-08T06:24:32.826Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.827Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.827Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-08T06:24:32.834Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.835Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.835Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-08T06:24:32.843Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.844Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-08T06:24:32.852Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-08T06:24:32.859Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.860Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.860Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-08T06:24:32.867Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.867Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.868Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-08T06:24:32.875Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.876Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:32.876Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:24:32.883Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:32.883Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:24:32.884Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.896Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.897Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:24:32.897Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.934Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.935Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:24:32.936Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.949Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.949Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-08T06:24:32.950Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.963Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.964Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:24:32.964Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.977Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.977Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:24:32.978Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:32.991Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:32.991Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:24:32.991Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:33.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:33.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:24:33.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:33.018Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:33.020Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-08T06:24:33.021Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-08T06:24:33.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:24:33.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-08T06:24:33.041Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.042Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.042Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-08T06:24:33.049Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.050Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.050Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-08T06:24:33.056Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.057Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.057Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-08T06:24:33.064Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.065Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-08T06:24:33.065Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:24:33.079Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:33.080Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.080Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-08T06:24:33.087Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-08T06:24:33.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-08T06:24:33.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:24:33.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-08T06:24:33.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:24:33.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:24:33.118Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.118Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-08T06:24:33.118Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:24:33.119Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:24:33.454Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? "}
{"@timestamp":"2025-07-08T06:24:33.454Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==> Parameters: HISOME(String), *************(String), 7777(String), gxsfhy(String), 8efc68adb41744d1810d2b6527ed32c5(String), 2025-07-08 14:24:33.452(Timestamp), 24022010563201889096002716053504(String)"}
{"@timestamp":"2025-07-08T06:24:33.470Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-08T06:24:33.478Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:24:33.481Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T06:24:33.482Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==> Parameters: 25070814243302254487670312957952(String), HISOME_fileServerUrl(String), 文件服务器地址(String), upperPlatDock(String), http://*************:8812(String), upperPlat(String), 2025-07-08 14:24:33.479(Timestamp), 2025-07-08 14:24:33.479(Timestamp)"}
{"@timestamp":"2025-07-08T06:24:33.498Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.499Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.500Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-08T06:24:33.507Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:24:33.508Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T06:24:33.509Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==> Parameters: 25070814243302254487670547838976(String), HISOME_fileServerChannel(String), 文件服务渠道(String), upperPlatDock(String), channel002(String), upperPlat(String), 2025-07-08 14:24:33.507(Timestamp), 2025-07-08 14:24:33.507(Timestamp)"}
{"@timestamp":"2025-07-08T06:24:33.524Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_documentService(String)"}
{"@timestamp":"2025-07-08T06:24:33.534Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:24:33.535Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T06:24:33.535Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==> Parameters: 25070814243302254487670774331392(String), HISOME_documentService(String), 文档服务(String), upperPlatDock(String), documentService002(String), upperPlat(String), 2025-07-08 14:24:33.534(Timestamp), 2025-07-08 14:24:33.534(Timestamp)"}
{"@timestamp":"2025-07-08T06:24:33.550Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.551Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-08T06:24:33.559Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:24:33.560Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T06:24:33.561Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==> Parameters: 25070814243302254487670992435200(String), HISOME_fileServiceAppId(String), 文件服务appId(String), upperPlatDock(String), fileAppId002(String), upperPlat(String), 2025-07-08 14:24:33.56(Timestamp), 2025-07-08 14:24:33.56(Timestamp)"}
{"@timestamp":"2025-07-08T06:24:33.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.577Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-08T06:24:33.585Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:24:33.585Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==>  Preparing: INSERT INTO jy_sys_dict ( id,t_code,t_name,t_type,t_value,t_catalog,create_time,update_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-08T06:24:33.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"==> Parameters: 25070814243302254487671202150400(String), HISOME_fileServiceAppSecret(String), 文件服务appSecret(String), upperPlatDock(String), fileAppSecret002(String), upperPlat(String), 2025-07-08 14:24:33.585(Timestamp), 2025-07-08 14:24:33.585(Timestamp)"}
{"@timestamp":"2025-07-08T06:24:33.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.JySysDictMapper.insertSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:24:33.604Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:24:33.605Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-08T06:24:33.612Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:24:33.639Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-08T06:24:33.643Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-08T06:24:37.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-08T06:24:37.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:24:37.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:24:37.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:24:37.683Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:24:40.760Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-08T06:24:40.761Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:24:40.762Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30932","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-08T06:31:09.574Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$65e76d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:10.121Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-08T06:31:10.361Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-08T06:31:10.365Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-08T06:31:10.373Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-08T06:31:16.045Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-08T06:31:16.048Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-08T06:31:16.294Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 233ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-08T06:31:16.414Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:31:16.671Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-08T06:31:17.132Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=63f5fcaa-c02d-3115-a6b0-28663195265e"}
{"@timestamp":"2025-07-08T06:31:18.064Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-08T06:31:18.070Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-08T06:31:18.084Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-08T06:31:18.193Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$a6c549a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.193Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$5ff78ed5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.362Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3f910234] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.636Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$49cd6a0f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.712Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$2570478c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.794Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$61917932] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.844Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.903Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.939Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$b4bf253e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:18.999Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$65e76d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-08T06:31:19.594Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:31:19.730Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:31:20.046Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:31:20.065Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-08T06:31:20.066Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-08T06:31:20.066Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-08T06:31:20.066Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-08T06:31:21.873Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-08T06:31:25.138Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-08T06:31:25.148Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:31:26.955Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-08T06:31:26.976Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-08T06:31:28.053Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:31:28.224Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@22f8a2c2],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@35c4686e],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@f6d381a],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@5ff839b0],]"}
{"@timestamp":"2025-07-08T06:31:28.225Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-08T06:31:40.439Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-08T06:31:40.448Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-08T06:31:42.515Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-08T06:31:44.083Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-08T06:31:44.313Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-08T06:31:44.329Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:31:44.347Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 9"}
{"@timestamp":"2025-07-08T06:31:44.366Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-08T06:31:44.366Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-08T06:31:44.366Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-08T06:31:44.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-08T06:31:44.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-08T06:31:44.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-08T06:31:44.368Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-08T06:31:44.368Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-08T06:31:44.369Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-08T06:31:44.372Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-08T06:31:48.514Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-08T06:31:48.514Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-08T06:31:48.514Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-08T06:31:48.681Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-08T06:31:52.076Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-08T06:31:54.706Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-08T06:31:56.121Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:31:56.121Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:31:56.121Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:31:56.122Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-08T06:31:56.566Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-08T06:31:56.567Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-08T06:31:56.664Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.s.i.JyCommonPlatformServiceImplTest","rest":"Started JyCommonPlatformServiceImplTest in 48.607 seconds (JVM running for 49.521)"}
{"@timestamp":"2025-07-08T06:31:56.685Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-08T06:31:56.686Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider\\eeip-standalone/config/application-alone.yml"}
{"@timestamp":"2025-07-08T06:31:56.693Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-08T06:31:56.749Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-08T06:31:56.788Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-08T06:31:56.788Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-08T06:31:56.802Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:31:56.803Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-08T06:31:56.803Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:31:56.818Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 294"}
{"@timestamp":"2025-07-08T06:31:56.821Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-08T06:31:56.821Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:31:56.822Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-08T06:31:56.836Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-08T06:31:57.175Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-08T06:31:57.183Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:31:57.183Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-08T06:31:57.183Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:31:57.183Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:31:57.183Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-08T06:31:57.184Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:31:57.186Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:31:57.186Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:31:57.186Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-08T06:31:57.186Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:31:57.187Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-08T06:31:57.188Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-08T06:31:57.189Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:31:57.190Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-08T06:31:57.205Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-08T06:31:58.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-08T06:31:58.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-08T06:31:58.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-08T06:31:58.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-08T06:31:58.505Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-08T06:31:58.509Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:31:58.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:31:58.521Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-08T06:31:59.274Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:31:59.275Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:31:59.283Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-08T06:31:59.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-08T06:31:59.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:31:59.470Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-08T06:32:00.829Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:32:00.831Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:00.837Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:32:00.886Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-08T06:32:00.888Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:00.894Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-08T06:32:00.968Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-08T06:32:00.969Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:00.978Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-08T06:32:01.037Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-08T06:32:01.038Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.049Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-08T06:32:01.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:32:01.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.325Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-08T06:32:01.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:32:01.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-08T06:32:01.529Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-08T06:32:01.530Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.538Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:32:01.573Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-08T06:32:01.574Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-08T06:32:01.574Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-08T06:32:01.575Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-08T06:32:01.575Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-08T06:32:01.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-08T06:32:01.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-08 14:32:01.577(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-08T06:32:01.596Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.596Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-08T06:32:01.596Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-08T06:32:01.596Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-08T06:32:01.674Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-08T06:32:01.683Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.685Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.686Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-08T06:32:01.694Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.694Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.694Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-08T06:32:01.701Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.702Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.702Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-08T06:32:01.709Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.711Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.711Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-08T06:32:01.718Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.719Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.719Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-08T06:32:01.728Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.729Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.729Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-08T06:32:01.737Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.737Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.738Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-08T06:32:01.745Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.745Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.745Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:32:01.752Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.753Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:32:01.754Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.767Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.767Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:32:01.768Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.780Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.781Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-08T06:32:01.781Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.795Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.796Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-08T06:32:01.796Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.810Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.811Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:32:01.811Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.824Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.825Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:32:01.826Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.838Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.839Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:32:01.839Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.852Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.852Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-08T06:32:01.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.866Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.868Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-08T06:32:01.869Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-08T06:32:01.878Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-08T06:32:01.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-08T06:32:01.886Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.887Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.888Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-08T06:32:01.895Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.895Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.895Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-08T06:32:01.903Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.903Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.904Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-08T06:32:01.910Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.911Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-08T06:32:01.911Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-08T06:32:01.924Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.924Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.924Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-08T06:32:01.933Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.935Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-08T06:32:01.935Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-08T06:32:01.952Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-08T06:32:01.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-08T06:32:01.953Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-08T06:32:01.953Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-08T06:32:01.959Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-08T06:32:01.960Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-08T06:32:01.960Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-08T06:32:01.960Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-08T06:32:02.307Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==>  Preparing: UPDATE jy_common_platform SET plat_type = ?,server_ip = ?,server_port = ?,app_id = ?,app_key = ?,update_time = ? WHERE id = ? "}
{"@timestamp":"2025-07-08T06:32:02.308Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"==> Parameters: JSJF(String), *************(String), 8173(String), 123456(String), 123456(String), 2025-07-08 14:32:02.306(Timestamp), 23122810335601849946738119460864(String)"}
{"@timestamp":"2025-07-08T06:32:02.326Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.updateByPrimaryKeySelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-08T06:32:02.327Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-08T06:32:02.327Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: JSJF_fileServerUrl(String)"}
{"@timestamp":"2025-07-08T06:32:02.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-08T06:32:02.358Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-08T06:32:02.365Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-08T06:32:06.626Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-08T06:32:06.626Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:32:06.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-08T06:32:06.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-08T06:32:06.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:32:06.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-08T06:32:06.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-08T06:32:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:32:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-08T06:32:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-08T06:32:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-08T06:32:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-08T06:32:07.661Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-08T06:32:07.662Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-08T06:32:07.663Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7668","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
