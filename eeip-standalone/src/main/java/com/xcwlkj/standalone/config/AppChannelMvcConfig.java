/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.standalone.config;

import com.xcwlkj.core.config.PcObjectMapper;
import com.xcwlkj.standalone.filter.PermissionsInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @version $Id: ThirdChannelMvcConfig.java, v 0.1 2018年8月28日 下午3:47:05 danfeng.zhou Exp $
 */
@SuppressWarnings("deprecation")
@Configuration
@EnableWebMvc
public class AppChannelMvcConfig extends WebMvcConfigurerAdapter {

    /**
     * @see WebMvcConfigurerAdapter#addResourceHandlers(ResourceHandlerRegistry)
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/META-INF/resources/",
            "classpath:/resources/", "classpath:/static/");
    }

    /**
     * @see WebMvcConfigurerAdapter#configureMessageConverters(List)
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        PcObjectMapper.buildMvcMessageConverter(converters);
    }

    /** 权限拦截器定义 **/
    @Autowired
    private PermissionsInterceptor permissionsInterceptor;

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(permissionsInterceptor).addPathPatterns("/**");
    }
}
